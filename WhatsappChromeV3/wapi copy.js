class WhatsappApi {
  async _envieMensagem(telefone, mensagem, delayDigitando = 2000, imagem, msg) {
    const tempoPorLetra = Math.floor(Math.random() * 20) + 15;
    let delaySendSeen = Math.random() * 600;

    delayDigitando = (tempoPorLetra) * (mensagem.length);

    if( msg && msg.tipo === 'CHAT' || msg.tipo === 'SAUDACAO' ) {
      delayDigitando = 0;
    } else if( msg && (msg.tipoDeNotificacao === 'ConfirmacaoPedido' || msg.tipoDeNotificacao === 'Cardapio') &&
      delayDigitando > 1000) {
      delayDigitando = Math.floor(Math.random() * 1500) + Math.floor(Math.random() * 1000) + 1000;
    }

    console.log('[WhatsappAPI] enviando mensagem: ' + mensagem);
    return new Promise(async (resolve, reject) => {
      try {
        const estahLogado = await this.estahLogado();
        console.log('[WhatsappAPI] está logado: ' + estahLogado);

        if (!this.chequeConexao() || !estahLogado) {
          console.log(this.obtenhaMensagemLog('\tNão está pronto para enviar'));
          return resolve({
            sucesso: false,
            status: 'NAO_PRONTO',
            mensagem: 'Não está pronto para enviar'
          });
        }

        const respostaContato = await this.verifiqueTelefone(telefone);

        console.log('[WhatsappAPI] verificacao telefone: ', respostaContato);
        if (respostaContato && !respostaContato.sucesso) {
          return resolve({
            sucesso: false,
            status: respostaContato.status,
            mensagem: 'Número não tem Whatsapp.'
          });
        }

        const contato = respostaContato.contato;

        telefone = contato.telefoneVerificado + '@c.us';

        console.log('[WhatsappAPI] tipo msg: ' + msg.tipo);

        try {
          var contatoWhatsapp = await WPP.contact.get(telefone);
          var chat = await WPP.chat.find(telefone);
        } catch (erro) {
          return resolve({
            sucesso: false,
            status: 'ERRO_CONTATO',
            mensagem: 'Erro ao buscar contato'
          });
        }

        var deveMarcarComoNaoLido = false;

        if( (chat && chat.unreadCount > 0) || !msg.marcarComoLida) {
          deveMarcarComoNaoLido = true;
        }
        if( contatoWhatsapp && contatoWhatsapp.isContactBlocked ) {
          resolve({
            sucesso: true,
            status: 'CONTATO_BLOQUEADO',
            mensagem: 'Contato está bloqueado'
          });
          return;
        }

        if( msg.tipo !== 'CHAT' && msg.tipo !== 'SAUDACAO') {
          try {
            const inicio = new Date();
            var ultimaMensagemEnviada = await this.obtenhaUltimaMensagem(contato.telefoneVerificado);

            if (ultimaMensagemEnviada && mensagem && ultimaMensagemEnviada.trim() === mensagem.trim()) {
              resolve({
                sucesso: true,
                idw: ultimaMensagemEnviada.id,
                status: 'ENVIADA',
                mensagem: 'Mensagem já havia sido enviada'
              });
              return;
            }
          } catch (erro) {
            // Continue mesmo se não conseguir verificar última mensagem
          }
        }

        try {
          if( document.hidden ) {
            await WPP.whatsapp.ChatPresence.sendPresenceAvailable();
          }

          const comandoSendSeen = await WPP.chat.markIsRead(telefone);

          if( msg.abrirChat ) {
            await WPP.chat.openChatBottom(telefone);
          }
        } catch (erro) {
          // Continue mesmo se houver erro nas operações de preparação
        }

        setTimeout(async () => {
          try {
            let resposta = await this.comandoDigitando(telefone);

            const delay = delayDigitando;

            console.log('\t[WhatsappAPI] vai esperar: ' + delay);

            setTimeout(async () => {
              try {
                console.log('\t[WhatsappAPI] vai mandar o comando');
                let resposta = null;
                const urlEncontrada = mensagem.match(regex_url);

                if( msg.temMenu ) {
                  const rows = [];
                  for( let i = 0; i < msg.menu.opcoes.length; i ++ ) {
                    rows.push({
                      rowId: msg.menu.opcoes[i].id,
                      title: msg.menu.opcoes[i].texto
                    })
                  }
                  var menu = {
                    "buttonText": msg.menu.textoBotao,
                    "listType": 1,
                    "description": mensagem,
                    "sections": [
                      {
                        "title": msg.menu.textoSecao,
                        "rows": rows
                      }
                    ]
                  };

                  if( msg.menu.tituloMensagem ) {
                    menu.title = msg.menu.tituloMensagem;
                  }

                  resposta = await WPP.chat.sendListMessage(telefone, menu);
                }
                else if( imagem ) {
                  const extensao = msg.imagem.split('.').pop();
                  const tipo = (extensao.indexOf('pdf?v=') !== -1 || extensao.indexOf('pdf') !== -1) ? 'document' : 'image';

                  resposta = await WPP.chat.sendFileMessage(
                    telefone,
                    imagem,
                    {
                      type: tipo,
                      caption: mensagem, // Optional
                      filename: 'arquivo.' + extensao
                    }
                  );

                  if( extensao.indexOf('pdf?v=') !== -1 || extensao.indexOf('pdf') !== -1 ) {
                    try {
                      let respostaTexto = await WAPI.sendMessageToID(telefone, mensagem);
                    } catch (erro) {
                      // Continue se falhar ao enviar texto adicional do PDF
                    }
                  }
                } else if (urlEncontrada && msg.enviarLinksBotao) {
                  resposta = await WPP.chat.sendTextMessage(telefone, mensagem, {
                    buttons: [
                      {
                        url: urlEncontrada[0].trim(),
                        text: 'Acesse o cardápio digital'
                      }
                    ],
                    footer: "Faça seu pedido pelo botão abaixo."
                  });
                } else {
                  if( msg.fazerPreview && urlEncontrada && msg.linkPreview ) {
                    //resposta = await WAPI.sendLinkWithAutoPreview(telefone, urlEncontrada[0], mensagem);
                    //resposta = await WAPI.sendMessageToID(telefone, mensagem);
                    resposta = WPP.chat.sendTextMessage(telefone, mensagem, {linkPreview: msg.linkPreview});
                  }
                  else {
                    resposta = await WAPI.sendMessageToID(telefone, mensagem);
                  }
                }

                try {
                  if( deveMarcarComoNaoLido ) {
                    let delayMarkAsUnred = Math.random() * 1500 + 1000;

                    setTimeout( async() => {
                      try {
                        await WPP.chat.markIsUnread(telefone);
                      } catch (erro) {
                        // Falha ao marcar como não lido não é crítica
                      }
                    }, delayMarkAsUnred);
                  }
                } catch (erro) {
                  // Continue mesmo se houver erro ao marcar como não lido
                }

                console.log('\t[WhatsappAPI] enviou: ' + resposta);
                if (resposta) {
                  resolve({
                    sucesso: true,
                    status: 'ENVIADA',
                    idw: resposta.id,
                    mensagem: 'Mensagem enviada com sucesso'
                  });
                }
                else {
                  resolve({
                    sucesso: false,
                    status: 'IMPOSSIVEL_ENVIAR',
                    mensagem: 'Falha ao enviar mensagem'
                  });
                }
              } catch (erro) {
                resolve({
                  sucesso: false,
                  status: 'ERRO_ENVIO',
                  mensagem: 'Erro durante o envio da mensagem'
                });
              }
            }, delay);
          } catch (erro) {
            resolve({
              sucesso: false,
              status: 'ERRO_PREPARACAO',
              mensagem: 'Erro na preparação do envio'
            });
          }
        }, delaySendSeen);

      } catch (erro) {
        resolve({
          sucesso: false,
          status: 'ERRO_GERAL',
          mensagem: 'Erro geral no envio de mensagem'
        });
      }
    });
  }
}