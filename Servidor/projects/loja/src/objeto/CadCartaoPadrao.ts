import { Injectable} from "@angular/core";
import {IFormCartaoRespostaValido} from "./IFormCartaoRespostaValido";
import {FormUtils} from "./FormUtils";

@Injectable({
  providedIn: 'root'
})
export class CadCartaoPadrao{
  constructor() { }

  ehValido(frm: any, frmElement: any): IFormCartaoRespostaValido {
    (frm as {submitted: boolean}).submitted = true;

    const ctrlInvalido = FormUtils.obtenhaControleInvalido(frm, frmElement);

    if( ctrlInvalido ) {
      return {
        valido: false,
        controle: ctrlInvalido
      }
    }

    return {
      valido: frm.valid,
      controle: null
    };
  }
}
