export class ObjetoComAdicionais {
  adicionais: any;
  valoresAdicionais: any;

  constructor() {
    this.adicionais = {}
  }

  atualizeTotal() {

  }

  configureAdicionais() {
    this.valoresAdicionais = []

    const nomesPropriedades = Object.keys(this.adicionais);
    for( let i = 0; i < nomesPropriedades.length; i++ ) {
      let nomeCampo = nomesPropriedades[i];
      if (nomeCampo.startsWith('lista')) {
        for(let nomeOpcao in this.adicionais[nomeCampo]) {
          if(nomeOpcao.startsWith('opcao') && this.adicionais[nomeCampo][nomeOpcao].selecionada) {
            const valor = this.obtenhaValorAdicional(this.adicionais[nomeCampo]);

            this.valoresAdicionais.push({
              nome: this.adicionais[nomeCampo][nomeOpcao].opcao.nome,
              qtde: this.adicionais[nomeCampo][nomeOpcao].qtde,
              valor: valor
            })
          }
        }
      }

      if(nomeCampo.startsWith('campo')) {


        this.valoresAdicionais.push({
          nome: this.adicionais[nomeCampo].nome,
          qtde: 1,
          valor: this.adicionais[nomeCampo].valor
        })
      }
    }
  }

  valideCampoAdicional(campoAdicional: any) {
    if(campoAdicional.esconder || campoAdicional.semOpcaoDisponivel) return true;

    if(campoAdicional.tipo === 'escolha-simples' ) {
      let valorCampo = this.adicionais['campo' + campoAdicional.posicao]

      if(campoAdicional.obrigatorio) {
        return valorCampo != null
      }
      return true
    }

    if(campoAdicional.tipo === 'multipla-escolha' ) {
      let valorCampo = this.adicionais['lista' + campoAdicional.posicao]

      let qtdMinima = campoAdicional.qtdMinima || 0;
      let qtdMaxima = campoAdicional.qtdMaxima || Object.keys(valorCampo).length;

      let qtdSelecionados = 0

      for(let nomeOpcao in  valorCampo) {
        let dadosOpcao = valorCampo[nomeOpcao]

        if(dadosOpcao && dadosOpcao.selecionada) {
          let qtde = dadosOpcao.qtde

          if(!qtde) qtde = 1

          qtdSelecionados += qtde
        }
      }

      if(qtdSelecionados === 0 && !campoAdicional.obrigatorio) return true;

      return qtdSelecionados >= qtdMinima && qtdSelecionados <= qtdMaxima
    }
  }

  obtenhaValorAdicionais() {
    let valorAdicionais = 0;

    for (let prop in this.adicionais) {
      if(prop.startsWith('campo') && this.adicionais[prop] != null)
        valorAdicionais += this.adicionais[prop].valor;
      else if(prop.startsWith('lista')) {
        let opcoes = this.adicionais[prop]

        valorAdicionais += this.obtenhaValorAdicional(opcoes)

      }
    }


    return valorAdicionais;
  }

  obtenhaValorAdicional(opcoes: any) {
    let valorAdicional = 0

    let tipoDeCobranca = opcoes.tipoDeCobranca

    switch(tipoDeCobranca) {
      case 'SOMA':
        for(let propInterna in opcoes) {
          if (opcoes[propInterna] && opcoes[propInterna].selecionada) {
            valorAdicional += opcoes[propInterna].valorTotal
          }
        }
        break;
      case 'MEDIA':
        let tot = 0;

        for(let propInterna in opcoes) {
          if (opcoes[propInterna] && opcoes[propInterna].selecionada) {
            tot += opcoes[propInterna].qtde;
            valorAdicional += opcoes[propInterna].valorTotal
          }
        }

        if(tot > 0)
          valorAdicional =  +((valorAdicional / tot).toFixed(2));
        break;
      case 'MAIOR':
        for(let propInterna in opcoes) {
          if (opcoes[propInterna] && opcoes[propInterna].selecionada) {
            if(opcoes[propInterna].valorTotal > valorAdicional)
              valorAdicional = opcoes[propInterna].valorTotal
          }
        }
        break;
    }

    return valorAdicional
  }
}
