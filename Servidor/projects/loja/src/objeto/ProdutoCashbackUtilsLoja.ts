
export class ProdutoCashbackUtilsLoja{
  static calculeCashbakDosProdutos(valorVenda: number, itens: any, atividade: any){
    if (!valorVenda) return 0;

    if (atividade.plano.valorMinimoPontuar && atividade.plano.valorMinimoPontuar > valorVenda)
      return 0;

    const totalBruto = itens.reduce((soma, item) => soma + (item.total), 0);
    const fatorDesconto: number = totalBruto > 0   ? ( valorVenda ) / totalBruto : 1;

    return itens.reduce((soma: number, item: any) => {
      // Criamos um novo objeto mantendo a referência do método
      const itemAjustado: any = fatorDesconto !== 1 ?
        Object.create(Object.getPrototypeOf(item), Object.getOwnPropertyDescriptors(item)) : item;
      // Ajusta o valor do item considerando o desconto proporcional
      itemAjustado.total =  item.total * fatorDesconto;

      const cashbackProduto: number = itemAjustado.obtenhaPontosFidelidade(atividade);

      item.cashback = cashbackProduto;

      // Usa a função obtenhaPontosFidelidade() com o valor ajustado
      return soma + cashbackProduto
    }, 0);

  }
}
