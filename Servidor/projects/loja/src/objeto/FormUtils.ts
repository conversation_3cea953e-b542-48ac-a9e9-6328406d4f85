import {NgForm} from "@angular/forms";
import {ElementRef} from "@angular/core";

export class FormUtils {
  static foqueInvalido($janela: any, controle: any) {
    controle.scrollIntoView();
    $janela.scrollTo(0, $janela.scrollTop - 30);

    if( controle.focus ) controle.focus();
  }

  static obtenhaControleInvalido(frm: NgForm, frmElement: ElementRef) {
    if( frm.valid ) {
      return null;
    }

    for(const key of Object.keys(frm.controls)) {
      const controle = frm.controls[key];

      if( !controle.valid ) {
        const input = frmElement.nativeElement.querySelector('#' + key);

        return input;
      }
    }

    return null;
  }
}
