import {IUITipoDePontuacaoLoja} from "./IUITipoDePontuacaoLoja";
import {FormatadorPontos} from "./FormatadorPontos";

export class IUITipoDePontuacaoPorPontos implements IUITipoDePontuacaoLoja {
  constructor(private empresa: any, private tipoDePontuacao: any, private tipoDeAcumulo: string, private atividade: any) {
  }

  calculePontos(valorVenda: number) {
    if(!valorVenda) return 0;
    return  Math.floor(valorVenda * this.tipoDePontuacao.pontosPorValor );
  }

  obtenhaPontosFormatado(valorVenda: number) {
    let pts: any = this.calculePontos(valorVenda);

    return FormatadorPontos.obtenhaPtsFormatado(this.tipoDePontuacao, this.tipoDeAcumulo, pts)
  }
}
