import {IUITipoDePontuacaoPorValor} from "./IUITipoDePontuacaoPorValor";
import {IUITipoDePontuacaoPorPontos} from "./IUITipoDePontuacaoPorPontos";
import {IUITipoDePontuacaoPorCashback} from "./IUITipoDePontuacaoPorCashback";
import {IUITipoDePontuacaoLoja} from "./IUITipoDePontuacaoLoja";
import {IUITipoDePontuacaoQtdeFixaAtividade} from "./IUITipoDePontuacaoQtdeFixaAtividade";
import {IUITipoDePontuacaoQtdeFixa} from "./IUITipoDePontuacaoQtdeFixa";
import {IUITipoDePontuacaoQtdeVariavelAtividade} from "./IUITipoDePontuacaoQtdeVariavelAtividade";

const mapClass: any = {}

mapClass['por-valor'] = IUITipoDePontuacaoPorValor;
mapClass['por-pontos'] = IUITipoDePontuacaoPorPontos;
mapClass['cashback'] = IUITipoDePontuacaoPorCashback;
mapClass['qtde-fixa'] = IUITipoDePontuacaoQtdeFixa;
mapClass['qtde-fixa-por-atividade'] = IUITipoDePontuacaoQtdeFixaAtividade;
mapClass['qtde-variavel-por-atividade'] = IUITipoDePontuacaoQtdeVariavelAtividade;

export class IUITipoPontuacaoFactory {
  static factory<T>(type: { new(empresa: any, tipoDePontuacao: any, atividade: any, tipoDeAcumulo: string): T },
                    empresa: any, tipoDePontuacao: any, tipoDeAcumulo: string, atividade: any): T {

    return new type(empresa, tipoDePontuacao, tipoDeAcumulo, atividade);
  }

  static crie(empresa: any, integracao: any): IUITipoDePontuacaoLoja {
    integracao.atividade.plano = integracao.plano;

    return IUITipoPontuacaoFactory.factory(mapClass[integracao.plano.tipoDePontuacao.tipo], empresa,
      integracao.plano.tipoDePontuacao, integracao.plano.tipoDeAcumulo, integracao.atividade);
  }
}
