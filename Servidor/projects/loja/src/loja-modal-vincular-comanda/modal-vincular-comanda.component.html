<kendo-dialog [width]="'98%'" [minWidth]="300" [maxHeight]="600">
  <ng-container *ngIf="!carregando; else loadingTemplate">
    <kendo-dialog-titlebar>
      Vincular Comanda
    </kendo-dialog-titlebar>

    <div class="form-container" [hidden]="leituraCartaoAberta">
      <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
            novalidate #frm="ngForm" (ngSubmit)="enviar()">

        <div class="form-group">
          <label for="codigo">Digite ou leia o código da comanda:</label>
          <input kendoTextBox
                 [(ngModel)]="codigo" #codigoD="ngModel"
                 id="codigo"
                 name="codigo"
                 required
                 placeholder="Ex: 1310"
                 appAutoFocus />
          <div class="invalid-feedback">
            <p *ngIf="frm.submitted && codigoD?.errors?.required">O código da comanda é obrigatório</p>
          </div>
        </div>

        <button kendoButton look="outline" icon="qr-code" class="btn-block" (click)="lerQRCode()" type="button">
          Ler QR Code
        </button>

        <button kendoButton themeColor="primary" class="btn-block"  type="submit">
          Fazer Pedido
        </button>

        <div *ngIf="erro" class="erro mt-2">
          {{ erro }}
        </div>
      </form>
    </div>


    <!-- Dialog de leitura de cartão do cliente -->
    <div class="cartao-cliente-dialog" *ngIf="leituraCartaoAberta">
      <div class="cartao-cliente-overlay" (click)="fecharLeituraCartao()"></div>
      <div class="cartao-cliente-container">
        <!-- Cabeçalho (ocupando largura total) -->
        <div class="cartao-cliente-header">
        </div>

        <!-- Layout de duas colunas -->
        <div class="cartao-cliente-duas-colunas">
          <!-- Coluna esquerda (Scanner) -->
          <div class="coluna-scanner">
            <div class="scanner-container">
              <zxing-scanner (scanSuccess)="onCodeResult($event)"></zxing-scanner>
              <button kendoButton look="outline" (click)="fecharLeituraCartao()">Cancelar Leitura</button>
            </div>
          </div>

          <!-- Coluna direita (Instruções) -->
          <div class="coluna-instrucoes">
            <div class="instrucoes-container">
              <div class="instrucao-texto">
                <h3><i class="fas fa-qrcode"></i> Leitura de Comanda</h3>
                <p><strong>Posicione sua COMANDA</strong> na frente da câmera para ler o código dela e concluir seu pedido.</p>
                <p><i class="fas fa-info-circle"></i> Mantenha a uma distância de aproximadamente 10 cm para melhor leitura.</p>
              </div>
              <div class="instrucao-imagem">
                <img src="/assets/images/qrcode_reader.png" alt="Posicione o QR Code">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <kendo-dialog-actions>
      <button kendoButton (click)="fechar()">Cancelar</button>
    </kendo-dialog-actions>
  </ng-container>

  <ng-template #loadingTemplate>
    <div class="loading">
      <i class="k-icon k-i-loading"></i>
      <p>Processando pedido...</p>
    </div>
  </ng-template>
</kendo-dialog>
