import {Component, Input, OnInit} from '@angular/core';

import { DialogRef } from '@progress/kendo-angular-dialog';
import {PedidosService} from "../services/pedidos.service";

@Component({
  selector: 'app-modal-vincular-comanda',
  templateUrl: './modal-vincular-comanda.component.html',
  styleUrls: ['./modal-vincular-comanda.component.scss']
})
export class ModalVincularComandaComponent implements OnInit{
  @Input() mesa: any = {};
  codigo = '';
  carregando = false;
  erro: string | null = null;
  selectedDevice: MediaDeviceInfo | null = null;
  // Propriedades para leitura de cartão do cliente
  leituraCartaoAberta = false;

  videoElement: HTMLVideoElement;
  barcodeDetector: any;
  streamAtivo: MediaStream | null = null;
  dispositivosDisponiveis: MediaDeviceInfo[] = [];
  dispositivoAtual  = '';
  leituraEmProgresso = false;
  constructor(private pedidosService: PedidosService, private dialogRef: DialogRef
  ) {

  }

  async enviar() {
    this.erro = null;
    if(!this.codigo){
      this.erro = `Informe ou leia o codigo da comanda`
      return;
    }

    this.carregando = true;
    try {
      let comanda: any = await this.pedidosService.obtenhaComandaDoCartao(this.mesa, this.codigo).catch((err: string) => {
        this.erro = err ;
      });

      if(comanda) {
        comanda.codigoCartaoCliente = this.codigo;
        this.dialogRef.close(comanda);
      }
    } catch (e: any) {
      this.erro = e.message || 'Erro ao vincular comanda.';
    } finally {
      this.carregando = false;
    }
  }

  lerQRCode() {
    this.leituraCartaoAberta  = true;
    this.codigo = '';

    this.erro = null;
  }





  pararCamera(): void {
    this.leituraEmProgresso = false;
  }




  onCodeResult(result: string) {
    if(result){
      this.codigo = result;
    } else {
      // this.exibaNotificacao('Por favor, informe o código do cartão do cliente', 'error');
    }

    this.leituraCartaoAberta = false;
    this.fecharLeituraCartao();
  }

  fecharLeituraCartao(): void {
    this.leituraCartaoAberta = false;
    this.pararCamera();
  }

  fechar() {
    this.dialogRef.close(false);
  }

  ngOnInit(): void {
    if(this.mesa.codigoCartaoCliente)
      this.codigo = this.mesa.codigoCartaoCliente
  }
}
