<app-header-tela [titulo]="'Extras do Pedido'" [exibirFechar]="true" (fechou)="fecheTela()" ></app-header-tela>

<div *ngFor="let campoAdicional of camposAdicionais; let posicao = index">
  <app-site-campo-adicional #adicionalComponent [id]="'adicional_' + campoAdicional.id" [campoAdicional]="campoAdicional"
                            (onDesmarcouOpcao)="desmarcouNovaOpcao($event)"  (onMarcouOpcao)="escolheuNovaOpcao($event)"
                            [itemPedido]="pedido" [posicao]="campoAdicional.posicao"></app-site-campo-adicional>
</div>


<footer class="footer"  >
  <div  >
    <p *ngIf="erro" class="text-danger"><b>{{erro}}</b></p>
    <div class="row">
      <div class="col">
        <button class="btn btn-primary btn-block" (click)="finalizar()">
           {{( !pedido.informouFormaDePagamento()) ? 'Ir para Pagamento' : 'Salvar'}}
        </button>
      </div>
    </div>
  </div>
</footer>
