import {Component, EventEmitter, Input, OnInit, Output, QueryList, ViewChild, ViewChildren} from '@angular/core';
import {ConstantsService} from "../../services/ConstantsService";
import {ItemPedido} from "../../objeto/ItemPedido";
import {SaborPizzaLoja} from "../../objeto/SaborPizzaLoja";
import {NgImageSliderComponent} from "ng-image-slider";
import {OrdenadorSabores} from "../../objeto/OrdenadorSabores";
import {AdicionalUtils} from "../../objeto/AdicionalUtils";

@Component({
  selector: 'app-adicionais-customizados',
  templateUrl: './adicionais-customizados.component.html',
  styleUrls: ['./adicionais-customizados.component.scss',
              '../site-campo-adicional/site-campo-adicional.component.scss']
})
export class AdicionaisCustomizadosComponent implements OnInit {
  @ViewChildren('nav') navs: QueryList<NgImageSliderComponent>
  @ViewChild("alerta", {static: false} ) alerta: any;
  exibirErros: any;
  @Input() itemPedido: ItemPedido;
  @Input() montarPizza = false;
  @Output() saboresSelecionados =  new EventEmitter();
  @Output() alterouTamanho =  new EventEmitter();
  pizzas: any = [];
  saboresPizza: any = [];
  saboresPizzaFiltrados: any = [];
  qtdeSabores  = 1;
  cobrarMaiorPedacao: boolean;
  produto: any;
  listaQtde = []
  filtroSabor: any;
  clicouBusca: boolean;
  private timerBusca;
  recalculandoSabores: boolean;
  constructor(private constantsService: ConstantsService) { }
  empresa: any;

  ngOnInit(): void {
    this.pizzas = this.constantsService.produtosLoja.filter( produto => produto.tipo === 'pizza');
    this.produto = this.itemPedido.produto;
    this.constantsService.empresa$.subscribe( empresa => this.empresa = empresa || {});

    if(this.itemPedido.sabores.length)
      this.qtdeSabores = this.itemPedido.sabores.length;

    if(this.produto.tamanhos.length === 1){
      this.itemPedido.produtoTamanho = this.produto.tamanhos[0];
      this.alterouTamanhoProduto();
    } else {
      this.setSaboresDoTamanho();
    }
  }

  desselecioneSabores(){
    this.saboresPizza.forEach( sabor =>  sabor.qtde =  sabor.obrigatorio ? 1 : 0)
  }

  alterouTamanhoProduto(inputElement: any = null){
    if(this.qtdeSabores > this.totalSaboresSelecionados())
      this.desselecioneSabores();

   setTimeout( () => {
     this.recalculandoSabores = true;
     this.setSaboresDoTamanho();
     this.obtenhaSaboresEscolhidos();
     this.itemPedido.atualizeTotal();
     this.alterouTamanho.emit(this.itemPedido.produtoTamanho)
     this.recalculandoSabores = false;

     if(inputElement)
       this.focoProximoAdicional(inputElement)

   }, 0)
  }

  alterouQtdeSabores(inputElement: any = null){
   if(!this.saboresPizza.length)
      this.setSaboresDoTamanho();

    if(this.qtdeSabores < this.totalSaboresSelecionados())
      this.desselecioneSabores();

    this.obtenhaSaboresEscolhidos();
    this.itemPedido.atualizeTotal();

    this.focoProximoAdicional(inputElement)
  }

  focoProximoAdicional(inputElement){
    setTimeout(() => {
      if(inputElement.nextElementSibling){
        console.log(inputElement.nextElementSibling)
        inputElement.nextElementSibling.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
      }   else {
        inputElement = inputElement.parentNode;
        //proximo adiconal gerado outro tela
        if(inputElement && inputElement.nextElementSibling){
          console.log(inputElement.nextElementSibling)
          inputElement.nextElementSibling.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
        }
      }
    }, 50)
  }

  setSaboresDoTamanho() {
    this.setQtdeSabores();
    this.saboresPizza = [ ];


    if(this.itemPedido.produtoTamanho){
      let manterSelecaoSabores = this.itemPedido.produtoTamanho.qtdeSabores >= this.qtdeSabores;

      if(!manterSelecaoSabores)
        this.qtdeSabores = 1;

      this.pizzas.forEach( (produtoPizza: any) => {
        let tamanhoPizzaAdicional =
          produtoPizza.tamanhos.find(tamanho => tamanho.template.id === this.itemPedido.produtoTamanho.template.id)

        if(tamanhoPizzaAdicional){
          let saborTela: SaborPizzaLoja = new SaborPizzaLoja(produtoPizza, tamanhoPizzaAdicional);

          saborTela.obrigatorio = produtoPizza.id === this.itemPedido.produto.id

          if(this.itemPedido.sabores.length && manterSelecaoSabores){
            let saboresProdutoNoPedido =
              this.itemPedido.sabores.filter( saborSelecionado => saborSelecionado.produto === produtoPizza.id);

            saboresProdutoNoPedido.forEach((saborPedido: any) => {
              saborTela.qtde += saborPedido.qtde;
            })
          } else if( saborTela.obrigatorio){
            saborTela.qtde = 1;
          }

          this.saboresPizza.push(saborTela)
        }
      })
    }

    this.saboresPizzaFiltrados = [...this.saboresPizza]

    this.saboresPizzaFiltrados = OrdenadorSabores.ordene(this.saboresPizzaFiltrados, this.itemPedido.produto.template.campoOrdenar,
      this.empresa ?  this.empresa.agruparCategoriasPizza : false)

        this.cobrarMaiorPedacao = this.produto.template.tipoDeCobranca === 'maior';

  }

  diminuirQtdeSabor(saborPizza: any){
    if(saborPizza.obrigatorio && saborPizza.qtde === 1) return;

    if(saborPizza.qtde > 0){
      saborPizza.qtde--;
      this.obtenhaSaboresEscolhidos();
      this.itemPedido.atualizeTotal();
      this.saboresSelecionados.emit( this.itemPedido.sabores);
    }

  }

  aumentarQtdeSabor(saborPizza: any, saborInput: any){

    if(this.qtdeSabores > this.totalSaboresSelecionados()){
      saborPizza.qtde++;
      this.obtenhaSaboresEscolhidos();
      this.itemPedido.atualizeTotal();
      this.saboresSelecionados.emit( this.itemPedido.sabores);
    }

    if(this.totalSaboresSelecionados() === this.qtdeSabores)
       this.focoProximoAdicional(saborInput);

  }

  obtenhaSaboresEscolhidos(){
    let sabores: any = [],
      saboresSelecionados =  this.saboresPizza.filter( sabor => sabor.qtde > 0 );

    saboresSelecionados.forEach((saborSelecionado: any) => {
      for (let i = 1; i <= saborSelecionado.qtde; i++){
        let sabor = Object.assign({}, saborSelecionado);
        sabor.qtde = 1;
        sabor.camposAdicionais = sabor.camposAdicionais.filter((adicionalSabor) =>
          adicionalSabor.obrigatorio &&
          !this.produto.camposAdicionais.find((adicionalProduto: any) =>  adicionalProduto.nome === adicionalSabor.nome))

        sabores.push(sabor);
      }
    })
    this.itemPedido.sabores = sabores;
    this.itemPedido.qtdeSabores  = this.qtdeSabores;
    AdicionalUtils.prepareAdicionaisSabores(  this.itemPedido, this.produto)
  }

  totalSaboresSelecionados() {
    return this.saboresPizza.filter( sabor => sabor.qtde > 0 ).reduce((sum: number, item: any) => item.qtde + sum, 0);
  }

  setQtdeSabores(){
    this.listaQtde = [];

    let maximo = this.itemPedido.produtoTamanho ? this.itemPedido.produtoTamanho.qtdeSabores :  1;

    for(let i = 1; i <= maximo; i++){
      this.listaQtde.push(i);
    }

  }

  setExibirErros(){
    this.exibirErros = true;
    setTimeout(() => {
      this.alerta.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
    }, 100);
  }

  valideCampos() {
    if(!this.itemPedido.produtoTamanho){
      this.setExibirErros();
      return false;
    }


    if(this.qtdeSabores > 1 && this.totalSaboresSelecionados()  !== this.qtdeSabores){
      this.setExibirErros();
      return false;
    }

    if(this.montarPizza && this.totalSaboresSelecionados() !== this.qtdeSabores){
      this.setExibirErros();
      return false;
    }

    return true;

  }

  onFilter() {
    if(this.timerBusca) clearTimeout(this.timerBusca);

    this.timerBusca = setTimeout( () => {
      if(!this.filtroSabor) {
        this.saboresPizzaFiltrados = OrdenadorSabores.ordene(this.saboresPizzaFiltrados, this.itemPedido.produto.template.campoOrdenar,
          this.empresa ?  this.empresa.agruparCategoriasPizza : false)
        return;
      }
      this.saboresPizzaFiltrados = []


      let sabor: SaborPizzaLoja;
      for(sabor of this.saboresPizza)
        if(sabor.obrigatorio) this.saboresPizzaFiltrados.push(sabor)
        else if (sabor.nome.toLowerCase().includes(this.filtroSabor.toLowerCase()) ||
          (sabor.descricao && sabor.descricao.toLowerCase().includes(this.filtroSabor.toLowerCase())))
          this.saboresPizzaFiltrados.push(sabor)

      this.saboresPizzaFiltrados =  OrdenadorSabores.ordene(this.saboresPizzaFiltrados, this.itemPedido.produto.template.campoOrdenar,
        this.empresa ?  this.empresa.agruparCategoriasPizza : false)
    }, 1000);

  }

  habilitarBusca() {
    this.clicouBusca = !this.clicouBusca
  }

  exibaFullScreen(i: number, saborPizza: any) {
    let id = 'nav' + i

    this.navs.find((item: NgImageSliderComponent, index: number, array: NgImageSliderComponent[]) => {
      let itemAny = item as any;
      if(itemAny.elRef.nativeElement.id === id)  {
        itemAny.imageOnClick(saborPizza)
        return true;
      }

      return false;
    })

  }

  abriuImagem($event: number) {

  }
}

