.headerCampo {
  margin-left: -10px;
  margin-right: -10px;
}


.radio label {
  font-weight: 500;
}

.radio label::before {
  margin-left: 0px;
}

.radio label::after {
  margin-left: -2px;
}

.btn-outline-light {
  border-color: transparent;
}

img.img-pequena {
  object-fit: cover;
  width: 60px;
  height: 60px;
  margin-right: 5px;
  cursor: pointer;
}

.headerCampo .badge {
  font-size: 11px;
}


label {
  font-size: 14px;
  color: var(--cor-texto-primaria, #333);
}

.k-checkbox {
  width: 22px;
  height: 22px;
}

.k-radio:checked, .k-checkbox:checked {
  border-color: #7e57c2;
  color: #ffffff;
  background-color: #7e57c2;
}

button {
  color: var(--cor-texto-botao, inherit) !important;
  background-color: var(--cor-botao, #007bff) !important;
}

/* Override for buttons inside botoes_mais_menos */
.botoes_mais_menos button {
  color: #787878 !important;
  background-color: transparent !important;
}

.k-radio:checked:focus, .k-checkbox:checked:focus {
  box-shadow: 0 0 0 2px rgba(126, 87, 194, 0.3);
}

.k-radio {
  width: 22px;
  height: 22px;
}

.nome_opcao {
  margin-left: 10px;
  position: relative;
  top: 1px;
  display: inline-block;
}

.desabilitada{
  .nome_opcao {
    color: #ccc;
  }
}

.preco-extra {
  margin-top: 4px;
  float:  right;
  background-color: var(--cor-preco-adicional) !important;
  color: var(--cor-texto-preco-adicional) !important;
}

.k-checkbox::before {
  width: 18px;
  height: 18px;
  font-size: 18px;
}

.alert {
  margin-left: -10px;
  margin-right: -10px;
  border-radius: 1px;
}
