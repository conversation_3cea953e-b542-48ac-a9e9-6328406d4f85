import { Component, OnInit } from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {PedidosService} from "../../services/pedidos.service";
import {AvaliacaoService} from "../../services/avaliacao.service";
import {ActivatedRoute, Router} from "@angular/router";
import {encode} from "base62";

@Component({
  selector: 'app-tela-avaliar-pedido',
  templateUrl: './tela-avaliar-pedido.component.html',
  styleUrls: ['./tela-avaliar-pedido.component.scss']
})
export class TelaAvaliarPedidoComponent implements OnInit, ITela {
  pedido = {
    id: -1,
    guid: '',
    horario: new Date(),
    itens: []
  }

  avaliacao = {
    comentario: '',
    gostouDaEntrega: null,
    nota: 0
  };
  avaliando = false;
  guid: '';
  msgErro: string = '';

  constructor(private router: Router, private activatedRoute: ActivatedRoute, private pedidosService: PedidosService,
              private avaliacaoService: AvaliacaoService) {
    this.activatedRoute.params.subscribe(params => {
      this.guid = params['guid'];
    });
  }

  ngOnInit(): void {
    this.pedidosService.obtenhaPorGuid(this.guid).then( (pedido) => {
      this.pedido = pedido;

      this.avaliacaoService.obtenhaPorIdPedido(this.pedido).then( (objAvalicao: any) => {
        if( objAvalicao ) {
          this.avaliacao = objAvalicao;
        }
      });
    });
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return false;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  fecheTela() {

  }

  enviarAvaliacao() {
    this.msgErro = '';

    if( this.avaliacao.nota === 0 ) {
      this.msgErro = 'Você deve selecionar uma nota.';
      return;
    }

    this.avaliando = true;

    this.avaliacaoService.envie(this.avaliacao, this.pedido).then( (res) => {
      this.router.navigateByUrl('/pedido/acompanhar/' + this.pedido.guid + '?msg=' +
        encodeURIComponent('Seu pedido foi avaliado!')).then( () => {});
    });
  }
}
