<div class="alert alert-danger mt-2" role="alert" *ngIf="mensagemErro" (close)="fecheMensagemErro()" dismissible>
  <i class="k-icon k-i-error"></i> {{mensagemErro}}
  <button type="button" class="close" data-dismiss="alert" aria-label="Fechar">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<form id="frm" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm">

  <div class="card  mt-0">
    <div class="card-body">
      <div class="bloqueio" *ngIf="carregando" >
        <div class="k-icon k-i-loading mt-5 centralizado fa-2x"  ></div>
      </div>
      <div class="row">
        <div class="col-12 col-sm-8">
          <div class="form-group ">
            <label for="numero">Número do cartão</label>

            <input type="text" class="form-control input-background" autocomplete="off"  [mask]="'0000-0000-0000-0009'" style="font-family: monospace;"
                   id="numero" name="numero" [(ngModel)]="cartao.numero" #numero="ngModel" #numeroElem appAutoFocus
                   required    data-pagarmecheckout-input>


            <i class="fa fa-credit-card fa-2x"     [ngClass]="{ 'text-muted': !cartao.bandeira,  ' text-success': cartao.bandeira && cartao.numeroValido}" > </i>


            <div class="invalid-feedback">
              <p *ngIf="numero.errors?.required">Obrigatório</p>
              <p *ngIf="numero.errors?.mask">Número do cartão de crédito inválido.</p>
            </div>
          </div>
        </div>

        <div class="col-12 col-sm-4">
          <div class="form-group">
            <label for="validade">Data validade</label>
            <kendo-dateinput name="validade" id="validade" format="MM/yyyy" class="form-control"  required #validade="ngModel"
                             [(ngModel)]="cartao.validade" data-pagarmecheckout-input>

            </kendo-dateinput>
            <div class="invalid-feedback">
              <p *ngIf="validade.errors?.required">Obrigatório</p>
            </div>
          </div>
        </div>

        <div class="col-12 col-sm-8">
          <div class="form-group ">
            <label for="nome">Nome do titular</label>
            <input type="text" class="form-control" autocomplete="off" required data-pagarmecheckout-input
                   id="nome" name="nome" [(ngModel)]="cartao.nome" #nome="ngModel"
                   placeholder="Nome impresso" value="" >
            <div class="invalid-feedback">
              <p *ngIf="nome.errors?.required">Obrigatório</p>
            </div>
          </div>
        </div>

        <div class="col-12 col-sm-4">
          <div class="form-group ">
            <label for="cvv">CVV</label>
            <input type="text" class="form-control" autocomplete="off" [mask]="'0009'" placeholder="cod. segurança"
                   id="cvv" name="cvv" [(ngModel)]="cartao.cvv" #cvv="ngModel" data-pagarmecheckout-input
                   required    >
            <div class="invalid-feedback">
              <p *ngIf="cvv.errors?.required">Obrigatório</p>
            </div>
          </div>
        </div>

        <div class="col-12 col-sm-4">
          <div class="form-group">
            <label>Tipo do Cartão</label>

            <kendo-combobox   name="tipoDoCartao" [(ngModel)]="cartao.tipoDoCartao"
                              [data]="tiposCartao" required
                              [textField]="'nome'" [valueField]="'id'"  #tipoDoCartao="ngModel"   class="form-control" >
            </kendo-combobox>

            <div class="invalid-feedback">
              <p *ngIf="tipoDoCartao.errors?.required">Obrigatório</p>
            </div>

          </div>
        </div>

        <div class="form-group  col-12 col-sm-8  "  >
          <label for="nome">Email</label>
          <input kendoTextBox id="email" name="email" placeholder="Informe seu email"
                 class="form-control"   #email="ngModel"
                 [(ngModel)]="cartao.email" required/>

          <div class="invalid-feedback">
            <p *ngIf="email.errors?.required">Email é obrigatório</p>
          </div>
        </div>

      </div>
    </div>
  </div>


</form>
