import {Component, ElementRef, EventEmitter, Inject, Input, OnInit, Output, Renderer2, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {DadosCartao} from "../../objeto/DadosCartao";
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {DOCUMENT} from "@angular/common";
import {FormUtils} from "../../objeto/FormUtils";
import {HttpClient} from "@angular/common/http";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";


@Component({
  selector: 'app-cad-cartao-pagarme',
  templateUrl: './cad-cartao-pagarme.component.html',
  styleUrls: ['./cad-cartao-pagarme.component.scss']
})
export class CadCartaoPagarmeComponent implements OnInit, ICartaoCreditoGateway {
  @ViewChild('frm', {static: true}) frm: NgForm;
  @ViewChild('frm', {read: ElementRef}) public frmElement: ElementRef;
  @Input() parcelamento: any;
  @Input() gateway: string;
  @Input() pedido: PedidoLoja;
  // tslint:disable-next-line:no-output-on-prefixsn
  @Output() onCriou = new EventEmitter();
  empresa: any;
  processando: any;
  mensagemSucesso: any;
  mensagemErro: any;
  cartao: any = new DadosCartao();
  bandeira: any = null;
  paymentMethod: any;
  parcelasValores = [];
  parcela: any;
  publicKey: string;
  carregando = true;
  cartaoTeste: any;
    constructor(private _renderer2: Renderer2,  protected http: HttpClient,
                private autorizacaoService: AutorizacaoLojaService,
              @Inject(DOCUMENT) private _document: Document) {
      this.cartaoTeste = {
        "number": '****************',
        "holder_name": "Comprador Teste",
        "exp_month":  "10",
        "exp_year":  (new Date().getFullYear() + 2).toString().substr(2),
        "cvv": "123"
      }
  }

  ngOnInit(): void {
    this.http.get(String(`/api/empresas/me/${this.gateway}/pk`)).toPromise().then((resp: any) => {
       if(resp.publicKey){
         this.publicKey = resp.publicKey;
         this.tokenizeCartao(this.cartaoTeste).then((res) => {
           this.carregando = false;
         }).catch((err) => {
           console.error(err)
           this.mensagemErro = 'Não será possível validar o cartão, informe a loja para habilitar o dominio de compra';
           this.carregando = false;
         })
       } else {
         this.mensagemErro =  resp.erro ?  resp.erro : 'Não foi possivel obter token da conta';
         this.carregando = false;
       }
    }).catch((erro) => {
      this.carregando = false;
      this.mensagemErro = 'Falha ou obter token'
    })

    let usuario = this.autorizacaoService.getUsuario();

    if(usuario && usuario.cpf) this.cartao.cpf = usuario.cpf
    if(usuario && usuario.email) this.cartao.email = usuario.email
  }

  setParcelamento(numeroParcelasFixas: number){
    if(numeroParcelasFixas){
      let total;

      if((this.pedido as any).cliente) { // pedido server
        total  = this.pedido.total;
      } else {
        total = this.pedido.obtenhaValorAhPagar();
      }

      for(let i = 1; i <= numeroParcelasFixas; i++){
        let parcela: any =  Number((total / i).toFixed(2));

        parcela = parcela.toFixed(2).replace('.', ',')

        this.parcelasValores.push({id: i, descricao: String(`${i}x de R$ ${parcela} sem juros`)})
      }
    }

  }

  tokenizeCartao(card: any){
    let dadosCartao: any = {
      type: "card",
      card: card
    }

    return new Promise( (resolve, reject) => {
      this.http.post('https://api.pagar.me/core/v5/tokens?appId=' + this.publicKey, dadosCartao)
        .toPromise().then( (res: any) => {
        console.log(res);
        resolve(res);
      }).catch((fail: any) => {
        let erro = 'Cartão inválido';
        if(fail.status === 0)
          erro = 'Não foi possível criar token do cartão, informe a loja para habilitar o dominio de compra'

        if(fail.error && fail.error.errors && fail.error.errors['request.card'])
          erro += ":" +   fail.error.errors['request.card'][0]

        reject(erro)
      } )
    })

  }

  crieTokenCartao(enderecoCobranca: any): Promise<DadosCartao> {
    return new Promise( (resolve, reject) => {
      if(this.parcela)
        this.cartao.parcela =  this.parcela.id;

      let dadosCartao: any = {
          "number": this.cartao.numero,
          "holder_name": this.cartao.nome,
          "exp_month":  (this.cartao.validade.getMonth() + 1),
          "exp_year":  this.cartao.validade.getFullYear().toString().substr(2),
          "cvv": this.cartao.cvv
      }

      this.tokenizeCartao(dadosCartao).then((res: any) => {
        let tokenize: any = { card: dadosCartao};

        tokenize.publicKey =  this.publicKey;
        tokenize.dataExpiracao =  new Date().setSeconds(new Date().getSeconds() + 55); //60s

        this.cartao.setRetornoTokenCartaoPagarme(res, tokenize);
        resolve(this.cartao);
      }).catch((err: any) => {
        reject(err)
      })
    })
  }

  ehValido(): any {
    (this.frm as { submitted: boolean }).submitted = true;

    const ctrlInvalido = FormUtils.obtenhaControleInvalido(this.frm, this.frmElement);

    if (ctrlInvalido) {
      return {
        valido: false,
        controle: ctrlInvalido
      }
    }

    return {
      valido: this.frm.valid,
      controle: null
    };
  }

  exibaCartao(dadosCartao: DadosCartao) {
  }

  alterouNumeroCartao() {

  }

  fecheMensagemErro(){
    delete this.mensagemErro;
  }
}
