<div *ngIf="!buscouUmEndereco">
  <h4>Onde você quer receber seu pedido?</h4>
  <div class="input-group" [hidden]="exibirBuscaGoogle">
    <input kendoTextBox #txtBusca id="busca" name="busca"
           class="form-control"  [readonly]="calculandoTaxa"
           [(ngModel)]="enderecoEscolhido.nome"
           #busca=ngModel
           allowCustom="true"
           appAutoFocus="true"
           placeholder="Informe nome da sua rua e bairro. Exemplo: Rua 3, Centro"
           (keyup)='keyUp.next($event)'
           required>
    <span class="input-group-prepend">
              <button class="btn btn-secondary" tabindex="-1" type="button" style="border-top-right-radius: 3px;border-bottom-right-radius: 0px;width: 55px;">
                  <i class="fa fa-search" *ngIf="!buscandoAutocomplete"></i>
                <div class="k-i-loading ml-1 mr-1" style="font-size: 20px;" *ngIf="buscandoAutocomplete" ></div>
              </button>
        </span>
  </div>

  <app-address-autocomplete-places  *ngIf="exibirBuscaGoogle"
                    [empresa]="empresa"   (onSelecionou)="escolheuEnderecoGoogle($event)">

  </app-address-autocomplete-places>

  <div style="height: 10px"></div>
  <div *ngFor="let dataItem of data;  let i=index" class="endereco">
    <li class="media pt-2 pb-2" *ngIf="i < 4" (click)="selecionou(dataItem)">
      <div class="mr-3">
        <i class="fas fa-map-marker-alt text-muted" style="font-size: 35px;"></i>
      </div>
      <div class="media-body">
        <h5 class="mt-0 mb-1">{{ dataItem.nome }}</h5>
        <span>
          {{ dataItem?.bairro}} - {{ dataItem.cidade?.nome }} / {{dataItem.estado?.nome}}
          <span style="">~{{(dataItem.distancia / 1000.0) |  number : '1.2-2'}} KM</span>
        </span>
      </div>
    </li>
  </div>
  <div class="endereco">
    <li class="media pt-2 pb-2" *ngIf="txtBusca.value && buscouAutoComplete && !buscandoAutocomplete" (click)="digitarEnderecoManualmente()">
      <div class="mr-3">
        <i class="fas fa-map-marker-alt text-muted" style="font-size: 40px;"></i>
      </div>
      <div class="media-body">
        <h5 class="mt-0 mb-1 text-danger">Não achei meu endereço</h5>
        <span class="text-muted">
        Clique aqui para informar endereço completo
        </span>
      </div>
    </li>
  </div>

  <div *ngIf="enderecosContato && enderecosContato.length && (!data || !data.length) ">
    <h4 >Usar endereço existente

      <i class="k-icon k-i-loading" *ngIf="calculandoTaxa"></i>
    </h4>

    <ul style="padding: 0;">
      <li *ngFor="let endereco of enderecosContato" class="mt-1">

        <button  class="btn btn-outline-light btn-block" type="button" [disabled]="calculandoTaxa" (click)="caculeTaxaEntrega(endereco)">
          <i class="fas fa-map-marker-alt text-muted fa-2x float-left mr-1"></i>

          <span class="abreviar d-block text-left ml-1">
            {{endereco.descricaoCompleta}}
          </span>

        </button>
      </li>
    </ul>

  </div>

</div>
<div [hidden]="!buscouUmEndereco || posicionarNoMapa">
  <form id="frmEndereco" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
        novalidate #frm="ngForm" (ngSubmit)="onSubmit($event)">

    <div class="form-group mb-2" *ngIf="false">
      <label  >Nome Local</label>
      <input kendoTextBox   name="descricao"
             class="form-control" appAutoFocus [autoFocus]="true" #descricao="ngModel"
             [(ngModel)]="endereco.descricao" required/>

      <small id="emailHelp" class="form-text text-muted">Exemplos: Casa, Trabalho</small>

      <div class="invalid-feedback">
        <p *ngIf="descricao.errors?.required">Nome do Local é obrigatório</p>
      </div>
    </div>
    <div class="form-group mb-2" >
      <label for="cep">CEP</label>
      <div class="input-group" style="max-width: 250px">
        <input kendoTextBox #txtCEP (change)="alterou()"
               type="text" class="form-control" autocomplete="off"
               id="cep" name="cep" [(ngModel)]="endereco.cep" #cep="ngModel"
               [mask]="mask"
               [readonly]="escolheuUmEnderecoServer"
               placeholder="Informe o CEP." value="" [required]="cepObrigatorio"/>

        <span class="input-group-prepend">
                <button class="btn btn-secondary" tabindex="-1" type="button" (click)="alterou()" style="border-top-right-radius: 3px;border-bottom-right-radius: 0px; ">
                    <i class="fa fa-search" *ngIf="!buscandoCEP"></i>
                  <div class="k-i-loading ml-1 mr-1" style="font-size: 20px;" *ngIf="buscandoCEP" ></div>
                </button>
          </span>
      </div>
      <small id="cepHelp" class="form-text text-muted">CEP {{cepObrigatorio ? '' : 'não'}} é obrigatório </small>

      <div class="invalid-feedback">
        <p *ngIf="cep.errors?.required">CEP é obrigatório</p>
      </div>
      <div class="alert alert-danger alerta-personalizado mt-1 mb-2" role="alert" *ngIf="msgErroCEP">
        <i class="fas fa-exclamation-triangle"></i> {{msgErroCEP}}
      </div>
    </div>
    <span [hidden]="exibirDepoisCep && !buscouCep">
        <div class="form-group mb-2">
    <label for="logradouro">Logradouro</label>
    <input type="text" class="form-control" autocomplete="off"  logradouroValido
           sistema="{{empresa?.integracaoDelivery?.sistema}}"
           id="logradouro" name="logradouro" [(ngModel)]="endereco.logradouro" #logradouro="ngModel"
           [readOnly]="cepValidado && respostaCep.logradouro !== ''" #txtLogradouro
           placeholder="Logradouro: Rua, av. etc" value="" required>
    <div class="invalid-feedback">
      <p *ngIf="logradouro.errors?.required">Endereço é obrigatório</p>
      <p *ngIf="logradouro.errors?.logradouroInvalido">
        {{ logradouro.errors?.logradouroInvalido}}
      </p>
    </div>
  </div>

        <div class="row">
          <div class="form-group mb-2 col-4">
            <label for="numero">Número</label>
            <input type="number" class="form-control" autocomplete="off" #txtNumero  [min]="0"   [tamanhoMax]="6"
                   id="numero" name="numero" [(ngModel)]="endereco.numero" #numero="ngModel"
                   placeholder="Número" required >
            <div class="invalid-feedback">
              <p *ngIf="numero.errors?.required">Número é obrigatório</p>
            </div>
          </div>

          <div class="form-group mb-2 col" style="padding-left: 0px;" *ngIf="!selecionarBairroDaZona()">
            <label for="bairro">Bairro</label>
            <input type="text" class="form-control" autocomplete="off"
                   id="bairro" name="bairro" [(ngModel)]="endereco.bairro" #bairro="ngModel"
                   placeholder="Nome do bairro"   [required]="bairroObrigatorio"
                   [readOnly]="formaDoPedido && formaDoPedido.bloquearBairroAposCEP && cepValidado && endereco.bairro !== ''">
            <div class="invalid-feedback">
              <p *ngIf="bairro.errors?.required">Bairro é obrigatório</p>
            </div>
          </div>
        </div>

        <div class="row">
          <div *ngIf="entregaPorZona" class="col">
            <div class="form-group">
                <label>Selecione {{formaDoPedido?.selecionarBairroDaZona ? 'o bairro' : 'a região' }} onde você mora</label>
              <kendo-combobox id="zonaDeEntrega" name="zonaDeEntrega" [data]="zonasDeEntrega" placeholder="selecionar" (ngModelChange)="selecionouZona()"
                              #cboZonaDeEntrega class="form-control" appAutoFocus [autoFocus]="true"  [allowCustom]="false"
                              [clearButton]="zonasBackup.length === 0"
                              [required]="entregaPorZona"
                              #zona="ngModel" [(ngModel)]="endereco.zonaDeEntrega" [valueField]="'id'"
                              [textField]="'nome'">
              </kendo-combobox>
              <small  class="form-text text-muted" *ngIf="!formaDoPedido?.selecionarBairroDaZona">Escolha a região mais próxima da sua casa</small>
              <small   class="form-text text-muted" *ngIf="formaDoPedido?.selecionarBairroDaZona">Entregamos somente nos bairros listados. Outras regiões, entrar em contato por WhatsApp.</small>

              <div class="invalid-feedback">
                <p *ngIf="zona.errors?.required">
                  {{ formaDoPedido?.selecionarBairroDaZona ? 'Bairro é obrigatório' : 'Zona de entrega é obrigatório' }}</p>
              </div>
              <!-- <small id="emailHelp" class="form-text text-muted">Exemplos: Casa, Trabalho</small> -->
            </div>
          </div>
        </div>
        <div class="form-group mb-2">
          <label for="complemento">Complemento</label>
          <input type="text" class="form-control" autocomplete="off"
                 id="complemento" name="complemento" [(ngModel)]="endereco.complemento"   #complementoInput
                 #complemento="ngModel"
                 placeholder="lote, quadra, etc." value="" [required]="complementoObrigatorio">

          <div class="invalid-feedback">
            <p *ngIf="complemento.errors?.required">Complemento é obrigatório</p>
          </div>
        </div>

        <div class="form-group mb-2"  *ngIf="pontoReferencia">
          <label for="complemento">Ponto de referência</label>
          <input type="text" class="form-control" autocomplete="off"
                 name="pontoDeReferencia" [(ngModel)]="endereco.pontoDeReferencia"   #pontoDeReferencia
                 placeholder="ponto de referência" value="">

        </div>

        <div class="row">
          <div class="form-group mb-2 col-xs-12 col-sm-6">
          <label for="estado">Estado</label>
          <kendo-combobox id="estado" name="estado" [(ngModel)]="endereco.estado" [data]="estados"  [filterable]="false"
                          placeholder="Selecione um Estado" class="form-control"   [textField]="'nome'" [valueField]="'id'"
                          required   #estado="ngModel" [readonly]="cepValidado" #cboEstado
                          autocomplete="disabled"
                          (valueChange)="mudouEstado($event)">
          </kendo-combobox>
          <div class="invalid-feedback">
            <p *ngIf="estado.errors?.required">Estado é obrigatório</p>
          </div>
        </div>
          <div class="form-group mb-2 col-xs-12 col-sm-6">
          <label for="cidade">Cidade</label>
          <kendo-combobox id="cidade" name="cidade" [(ngModel)]="endereco.cidade" [data]="cidades" [filterable]="false"
                          placeholder="Selecione uma Cidade" class="form-control"  [textField]="'nome'" [valueField]="'id'"
                          #cboCidade  (ngModelChange)="alterouCidade()"
                          required #cidade="ngModel"  [readonly]="cepValidado ">
          </kendo-combobox>
          <div class="invalid-feedback">
            <p *ngIf="cidade.errors?.required">Cidade é obrigatória</p>
          </div>
        </div>
        </div>

    </span>


    <div class="alert alert-danger mt-2 mb-2" role="alert" *ngIf="msgErro">
      <i class="fas fa-exclamation-triangle"></i> {{msgErro}}
    </div>

    <footer class="footer" *ngIf="exibirBotao">
      <div>
        <div class="row" style="padding: 15px;">
          <div class="col">
            <button class="btn btn-blue btn-block" type="submit" [disabled]="calculandoTaxa" >
              <i class="k-icon k-i-loading" *ngIf="calculandoTaxa"></i>
              {{labelSalvarEndereco}}</button>
          </div>
        </div>
      </div>
    </footer>
  </form>
</div>

<ng-container *ngIf="posicionarNoMapa">
  <div style="margin-left: -12px;margin-right: -12px;margin-top: -22px;">
    <app-tela-posicao-mapa [latitude]="latitude" [longitude]="longitude"
                           [formEndereco]="this"></app-tela-posicao-mapa>
  </div>
</ng-container>
