.preco {
  color: #6db31b;
  display: inline-block;
  margin-right: 10px;

  &.antigo{
    color: #ccc;
    text-decoration: line-through;
  }
}

.produto {
  border-bottom: solid 1px #e8e8e8;
  min-height: 150px;

  &.destacado {
    height: auto;
  }

  cursor: pointer;

  .media-body{
    padding-top: 15px;
  }
}
.media .text-danger{
  color: #f1556c7a !important;
}
.produto {
  img {
    object-fit:contain;
    display: block;
    margin: 0 auto;
  }
}

.capa_empresa {
  height: 200px;
  background-size: cover;
}

.capa_empresa.centralizada {
  z-index: 10;
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
}

.cartao {
  background: white;
  margin-left: auto;
  margin-right: auto;
  padding: 15px;
}

.cartao.conteudo {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
}

h5.nome_produto {

}

.cartao.conteudo.topo {
  margin-top: 0px;
  width: 100%
}

/*
.cartao.conteudo {
  box-shadow: 0 4px 10px -2px #E2E3E3;
  min-height: 190px;
  border-top: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
*/


.cartao.semborda {
  margin-top: 20px;
  border: 0 none;
  background-color: transparent;
  padding: 0;
}

.bg-content {
  display: none;
}



.imagem_empresa {
  width: 80px;
  height: 80px;
  float: left ;
}

.detalhes_empresa  {
  float:left;
  margin-left: 10px;
  display: inline-block;
  width: calc(100% - 90px);
}

.nome_empresa {
  font-size: 24px;
  color: black;
  font-weight: 500;
  display: block;
  line-height: 1.2;
  max-height: 1.2em;
  overflow: hidden;
}

.endereco {
  font-size: 11px;
}

.whatsapp {
  display: block;
  margin-bottom: 5px;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}

.dados_empresa {
  min-height: 90px;
  overflow: hidden;
}

.linha {
  border-bottom: #EFEFEF solid 1px;
}

.descricao_empresa {
  margin: 10px;
  font-size: 12px;
  font-weight: 400;

}

.menu {
  color: #525252;
  margin-top: 15px;

}

.brinde {
  margin-top: 10px;
}
.valor {
  position: absolute;
  color: white;
  font-size: 20px;
  top: 10px;
  width: 100%;
  text-align: center;
}

.row {
  padding-left: 5px;
  padding-right: 5px;
}

.brinde {
  text-align: center;
  position: relative;
}

.preco_troca {
  font-weight: 600;
}

.nome_brinde {
  display: inline-block;
  margin-top: 5px;
  font-size: 16px;
  background: #4b4b4b;
  color: white;
  margin-left: 2px;
  padding: 5px 10px 5px 10px;
  border-radius: 50px;
  font-weight: 200;
}

.foto_brinde {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 30px;
  margin-top: 5px;
  width: 100%;
}


.foto_ambiente {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 20px;
}

.nome_brinde_pontos {
  font-size: 15px;
  font-weight: 600;

}

.botoes {
  margin: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
}

.botao {
  padding: 15px;
}

.botao.verde{
  background: #6DB31B;
  color:  white;
}

.botao.azul {
  border: #1c95d4 solid 1px;
  margin-top: 10px;
  color: #1c95d4;
}

.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}


.float{
  position:fixed;
  width:60px;
  height:60px;
  bottom:40px;
  right:40px;
  background-color:#25d366;
  color:#FFF;
  border-radius:50px;
  text-align:center;
  font-size:30px;
  box-shadow: 2px 2px 3px #999;
  z-index:100;
}

.my-float{
  margin-top:16px;
}


.fidelidade {
  height: 16px;

  width: 24px;
  background: #3B86FF;
  text-align: center;
  float:left;
  line-height: 1em;
  margin: 2px 3px;
}

.azul .coracao{
  display: inline-block;
  fill: white;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin: 0;
}

.azul {
  color: #3B86FF;
  font-weight: bold;
}


.bolinha {
  margin: 5px;
  width: 8px;
  height: 8px;
  background: #6DB31B;
  border-radius: 100px;
  float: left;
}

.horario {
  padding-top: 2px;
  margin-left: 7px;
  width: 100%;
}

.bolinha.fechado {
  background: red;
}

.horario .descricao {
  font-size: 11px;
  font-weight: bold;
}

.icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 5px;
}

.slides-fotos h3, .slides-produtos h3{
  text-align: center;
  line-height: 32px;
}

.slides-fotos, .slides-produtos {
  position: fixed;
  overflow: auto;
  z-index: 1000;
  top:0px;
  background: white;
  height: 100%;
  width: 100%;
}

.slides-produtos {
  padding-top: 60px;
  background: rgba(255, 255, 255, 0.9);
  height: 100%;
}

.cartao.descricao {
  margin-top: 15px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;

}

.grande {
  font-size: 18px;
  font-weight: bold;
}

.botao_produto {
  border: 1px solid black;
  padding: 15px;
  border-radius: 30px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
}

.botao_produto.verde {
  border: 1px solid #3fad36;
  color: #3fad36;

}

.titulo-produtos {
  background: white;
}
.slides-produtos .icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 0;
  margin-top: 9px;
}


@media screen and (min-width: 768px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos   {
    max-width: 90%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .bg-content {
    z-index: 0;
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 120%;
    height: 120%;
    margin-left: -10%;
    background-position: 0px 0px, 50% 50%;
    background-size: auto, cover;
    background-repeat: repeat, no-repeat;
    opacity: 1;
    -webkit-filter: blur(24px);
    filter: blur(24px);
    display: block;

  }

  .content {
    position: relative;
    z-index: 10;

  }

  .slides-fotos, .slides-produtos {
    left: 20%;
  }

  .sobre_nos {
    border-radius: 5px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
    padding: 10px;
    padding-bottom: 5px;
    background: white;
    margin-top: 10px;
  }
  .brinde {
    margin-top: 0 ;
  }


  .capa_empresa.centralizada {
    height: 310px;
    max-width: 90%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .capa_empresa.desfocada {
    height: 305px;
  }

  .cartao.conteudo {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 30px;
    border-top-right-radius: 0px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
  }

  .cartao.conteudo.topo {
    margin-top: 0px;
  }
}

.icone.insta {
  fill: #525252;
}

.cinza a {
  color: #525252 ;
}

.FlexEmbed {
  display: block;
  overflow: hidden;
  position: relative;
}

.FlexEmbed:before {
  content: "";
  display: block;
  width: 100%;
}


.FlexEmbed--2by1:before {
  padding-bottom: 25%;
}

.FlexEmbed.desfocada {
  background-position: 0px 0px, 50% 50%;
  background-size: auto, cover;
  background-repeat: repeat, no-repeat;
  opacity: 1;
  -webkit-filter: blur(12px);
  filter: blur(12px);
  z-index: -1;
  position: absolute;
  width: 100%;
  max-width: 100%;
  display: block;
  top: 0px;
}

.CoverImage {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 0 auto;
  max-height: 300px;
  max-width: 90%;
}

.megamenu{
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #dcdcdc;
  font-family: Arial;
  height: 50px;
  /* Links inside the navbar */
  a {
    float: left;
    font-size: 14px;
    text-align: center;
    padding: 7px 8px;
    text-decoration: none;
    color: #0b0b0b;
  }

  h4{
    color: #56167D;
    font-size: 14px;

  }

  /* Add a red background color to navbar links on hover */
  a:hover, .dropdown:hover .dropbtn {

  }

  /* The dropdown container */
  .dropdown {
    float: left;
    overflow: hidden;
    position: inherit;
    .dropbtn {
      font-size: 15px !important;
      padding: 12px 15px;
      border: none;
      outline: none;
      color: #333;
      background-color: inherit;
      font: inherit; /* Important for vertical align on mobile phones */
      margin: 0; /* Important for vertical align on mobile phones */
      text-transform: uppercase;
    }
  }


  /* Dropdown content (hidden by default) */
  .dropdown-content {
    border: 1px solid #dcdcdc;
    display: none;
    position: absolute;
    background-color: #fff;
    width: 100%;
    left: 0;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
  }


  /* Show the dropdown menu on hover */
  .dropdown:hover .dropdown-content {
    display: block;
  }

  /* Create three equal columns that floats next to each other */
  .coluna {
    float: left;
    width: 33.33%;
    padding: 10px;
    background-color: #fff;

    .grupo{
      &.esconder{
        max-height: 220px;
        overflow: hidden;
      }
    }
  }

  /* Style links inside the columns */
  .coluna a {
    float: none;
    background-color: #fff;
    padding: 5px;
    text-decoration: none;
    display: block;
    text-align: left;
  }

  /* Add a background color on hover */
  .coluna a:hover {
    background-color: #ddd;
  }

  /* Clear floats after the columns */
  .linha:after {
    content: "";
    display: table;
    clear: both;
  }
}

.left-side-menu{
  left: 0px;
  bottom: 0px;
  top: auto;
  width: 80%;
  transition: all .2s ease-out;
  z-index: 10010 !important;
  padding-top: 0px;

  .titulo{
    padding-top: 15px !important;
    background: #000000c9;
    padding-bottom: 15px !important;
    h4{
      color: #fff
    }
  }


  &.exibir{
    display: block;
  }

  .slimscroll-menu{
    overflow: scroll;
    max-height: 800px;
  }

  ul > li > a {
    color: #6e768e;
    display: block;
    padding: 10px 15px;
    position: relative;
    transition: all 0.4s;
    font-family: "Poppins", sans-serif;
    font-size: .875rem;
  }
}

.modal-backdrop  {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #333;
  opacity: .5;
  z-index: 10001 !important;
}

@media (max-width: 992px) {

  .megamenu{
    .coluna {
      width: 100%;
      .grupo{
        max-height: inherit;
        height: auto;
      }
    }
  }

  .nome_empresa {
    font-size: 16px !important;
  }

  .cartao.conteudo.topo {
    width: 100%;
  }
  .cartao{
    padding: 10px;
  }

  ::ng-deep.mat-tab-header {
    margin-left: -19px !important;
    margin-right: -19px !important;
  }

  .produto{
    .descricao {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;

    }
  }
}

@media (min-width: 992px) {
  .produto {
    width: calc(50% - 20px);
    display: inline-block;
    border: 1px solid #f2f2f2;
    box-shadow: 0px 1px 4px rgb(0 0 0 / 5%);
    border-radius: 4px;
    margin: 10px;
    padding: 8px;

    &:hover {
      border: 1px solid #e3e3e3;
    }

    .nome-produto{
      font-size: 16px;
    }
    .preco{
      font-size: 18px !important;
      &.antigo{
        font-size: 16px !important;
      }

    }

    .descricao {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      min-height: 35px;
    }
  }

  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 100%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .CoverImage {
    max-width: 100%;
  }

  .tirar_margem{
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

.header {
  position: sticky;
  top: -1px;
  background: #fff;
  z-index: 999;
}

.remova_padding {
  margin-left: -12px;
  margin-right: -12px;
}

.bg-light{
  background-color: #f7f8f8 !important;
}

::ng-deep .nav-tabs {
  overflow-x: hidden;
  overflow-y: hidden;
  flex-wrap: nowrap;
  padding-bottom: 2px;
  border-bottom: none;
  position: sticky;
  top: 55px;
  z-index: 9999;
  border-bottom: solid 1px #eaeaea;
  background: #fff;
  width: 100%;
}

::ng-deep .mobile .nav-tabs {
  overflow-x: scroll;
}

::ng-deep .mobile .nav-tabs::-webkit-scrollbar {
  display: none;
}

::ng-deep .nav-tabs .nav-link {
  font-size: 14px;
  color: #a6a5a5;
}

::ng-deep .nav-bordered li a {
  padding: 15px 20px !important;
}

::ng-deep .nav-tabs .nav-link.active, ::ng-deep .nav-tabs .nav-item.show .nav-link {
  background: #fff;
  font-weight: bold;
}

::ng-deep .mobile .nav-tabs {
  border-bottom: solid 1px #dadada !important;
  box-shadow: 0 3px 5px #e9e9e9;
}

::ng-deep .nav-tabs li {
  white-space: nowrap;
}

::ng-deep .tab-content {
  padding: 5px;
}

.tirar_margem {
  margin-left: -12px;
  margin-right: -12px;
}

.font-11{
  font-size: 11px;
}
.font-12{
  font-size: 12px;
}

.ver_todos {
  display: none;
  font-size: 14px;

  a {
    cursor: pointer;
  }
}

.arrow {
  display: none;
}

.ecommerce {
  .secaoCategoria.moveu {
    .arrow.esquerda {
      display: block;
    }
  }

  &.secaoCategoria.moveu {
    .arrow.esquerda {
      display: block;
    }
  }

  .secaoCategoria.scroll_fim {
    .arrow.direita {
      display: none;
    }
  }

  .produtos_categoria {
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .scroll_categoria {
    grid-auto-flow: column;
    display: grid;
    width: fit-content;
  }


  .produto {
    width: 210px;
    height: 145px;
    border: solid 1px #efefef;
    border-radius: 5px;
    padding: 5px;
    margin: 5px;
    padding-bottom: 0px !important;
    margin-bottom: 0px !important;

    .container_foto {
      /*
      height: 145px;
      display: flex;
      align-items: center;
      justify-content: center;
       */
    }

    .descricao {
      margin-top: 10px;
    }

    .preco {
      margin-top: 3px;
    }
  }
  .vitrine{
    .produto {
      min-height: 290px;
      img{
        max-height: 150px;
        min-height: 120px;
      }
    }

    .container_foto {
    }
  }

  .arrow {
    position: absolute;
    display: block;
    padding: 6px 12px;
    border: solid 1px #e2e2e2;
    background: #fff;
    border-radius: 100px;
    font-size: 16px;
    color: #2f2f2f;
    cursor: pointer;
    z-index: 1;
    top: 130px;

    &.direita {
      right: -10px;
    }

    &.esquerda {
      left: -15px;
      display: none;
    }
  }

  .ver_todos {
    position: absolute;
    right: 10px;
    top: 0px;
    display: block;
  }
}

@media only screen and (max-width: 768px) {
  .ecommerce {
    .produtos_categoria {
      overflow-x: auto;
    }

    .arrow {
      display: none;
    }
  }
}

::ng-deep .navImagem .ng-image-slider .image {
  object-fit: contain;
  cursor: pointer;
  right: auto !important;
}

::ng-deep .navImagem .ng-image-slider .ng-image-slider-container .main .main-inner .img-div {
  box-shadow: none;
}

hr {
  border-top: solid 1px #eae9e9;
}

::ng-deep .natal {
  h4 {
    &.categoria {
      color: #730101 !important;
    }
  }

  .tab_categoria {
    color: #d39797 !important;
  }

  .tab_categoria {
    &.active {
      color: #730101 !important;
      border-bottom: 2px solid #730101 !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}



::ng-deep .ano_novo {
  h4 {
    &.categoria {
      color: #030303 !important;
    }
  }

  .tab_categoria {
    color: #7f7f7f !important;
  }

  .tab_categoria {
    &.active {
      color: #030303 !important;
      border-bottom: 2px solid #f1dfa5 !important;
    }
  }

  .nav-bordered {
    border-bottom-width: 1px !important;
  }
}

.imagem_empresa2 {
  width: 42px;
  height: 42px;
  border-radius: solid 1px #ccc;
  float: left ;
  border-radius: 50px;
  margin-top: -8px;
}


.circle{
  position:relative;
  width:100px;
  height:100px;
  text-align: center;

  svg{
    fill:none;
    stroke: #cd486b;
    stroke-linecap: round;
    stroke-width:3;
    stroke-dasharray: 1;
    stroke-dashoffset: 0;
  }

  svg.svg_animacao {
    animation: stroke-draw 6s ease-out infinite alternate;
    //animation: stroke-draw 6s cubic-bezier(0.77, 0, 0.175, 1) infinite alternate;
  }

  img{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 73px;
    border-radius: 50%;
  }

  .div_label_empresa {
    bottom: -15px;
    width: 100%;
    position: absolute;
    left: 2px;
  }

  .label_empresa {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0 auto;
    width: 95px;
    font-size: 13px;
  }
  @keyframes stroke-draw {
    from{
      stroke: #c20032;
      stroke-dasharray: 3;
    }
    to{
      stroke:#cd486b;
      transform:rotate(180deg);
      stroke-dasharray: 8;
    }
  }
}

.col-empresa {
  padding-right: 0px;
  padding-left: 0px;
}

.div_preco {
  position: absolute;
  bottom: 5px;
  width: 100%;
}

.scrollx {
  overflow-x: scroll;
}
