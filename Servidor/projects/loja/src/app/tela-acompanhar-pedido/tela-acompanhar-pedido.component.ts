import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {CarrinhoService} from "../../services/carrinho.service";
import {PedidosService} from "../../services/pedidos.service";
import {ActivatedRoute} from "@angular/router";
import {timer} from "rxjs";
import {DominiosService} from "../../services/dominios.service";
import {EnumMeioDePagamento} from "../../objeto/EnumMeioDePagamento";
import {DialogCloseResult, DialogService} from "@progress/kendo-angular-dialog";
import {TelaCartaoDeCreditoComponent} from "../tela-cartao-de-credito/tela-cartao-de-credito.component";
import {DadosCartao} from "../../objeto/DadosCartao";
import moment from "moment";
import {MyDetectorDevice} from "../shared/MyDetectorDevice";
import {AvaliacaoService} from "../../services/avaliacao.service";
import {ThreeDSDialogComponent} from "./three-ds-dialog";
import {PagamentoPixQrcodeComponent} from "./pagamento-pix/pagamento-pix-qrcode.component";

declare var fbq;
@Component({
  selector: 'app-tela-acompanhar-pedido',
  templateUrl: './tela-acompanhar-pedido.component.html',
  styleUrls: ['./tela-acompanhar-pedido.component.scss']
})
export class TelaAcompanharPedidoComponent implements OnInit, ITela , OnDestroy {
  @ViewChild('telaPix', { static: false} ) telaPix: PagamentoPixQrcodeComponent;
  pedido: any = {};
  pagamento: any;
  empresa: any = {};
  cancelando: boolean;
  guid: string;
  timerPedidos;
  carregou: boolean;
  FormaDeEntrega = FormaDeEntrega;
  urlArir;
  acabouFazerPedido = false;
  EnumMeioDePagamento = EnumMeioDePagamento;
  falhaNoPagamentoOnline: boolean;
  autenticarPagamento: boolean;
  mensagemFalhaPagamento: string;
  dadosCartao: DadosCartao;
  telaPagamento: any;
  foiPago: boolean;
  foiReembolsado: boolean;
  tentandoNovoPagamento: boolean;
  textoHorarioEntregaAgendada: any;
  titulo = 'Detalhes do pedido';

  desktop = false;
  avaliacao: any = {};
  msg = '';
  dialogRefAutenticar: any;
  aguardandoTokenizar: boolean;
  buscandoPedido: boolean;
  retorno3ds: string;

  constructor(private carrinhoService: CarrinhoService, public  dominiosService: DominiosService,
              private activatedRoute: ActivatedRoute, private pedidosService: PedidosService,
              private dialogService: DialogService ,
              private detectorDevice: MyDetectorDevice, private avaliacaoService: AvaliacaoService) { }

  ngOnInit(): void {
    let state = window.history.state;

    this.falhaNoPagamentoOnline = state.falhaNoPagamentoOnline
    this.mensagemFalhaPagamento = state.mensagemFalhaPagamento

    if(state.aguardandoTokenizar) this.aguardandoTokenizar  = true;

    this.desktop = this.detectorDevice.isDesktop();
    this.guid =  this.activatedRoute.snapshot.params.guid;
    this.msg = this.activatedRoute.snapshot.queryParams.msg;

    if(state && state.pedido){
      this.urlArir = state.urlArir;
      this.empresa = state.empresa;
      this.pedido = state.pedido;
      this.falhaNoPagamentoOnline = state.falhaNoPagamentoOnline
      this.mensagemFalhaPagamento = state.mensagemFalhaPagamento

      this.acabouFazerPedido = true;
    } else {
      let dados: any = this.carrinhoService.obtenhaDadosUltimoPedido();

      if (dados && dados.pedido.guid === this.guid) {
        this.urlArir = dados.urlAbrir;
        this.empresa = dados.empresa;
        this.pedido = dados.pedido;
        if(this.pedido.horarioEntregaAgendada)
          this.textoHorarioEntregaAgendada =
            moment(this.pedido.horarioEntregaAgendada).utcOffset("-0400").format("DD/MM/YYYY [às] HH:mm:ss")

        this.falhaNoPagamentoOnline = dados.falhaNoPagamentoOnline
        this.mensagemFalhaPagamento = dados.mensagemFalhaPagamento
        this.acabouFazerPedido = true;
      }
      if(! this.guid && this.pedido)  this.guid = this.pedido.guid;
    }

    if(this.activatedRoute.snapshot.params.status3ds){
      this.retorno3ds = this.activatedRoute.snapshot.params.status3ds; // error or success
      if( this.retorno3ds  !== 'success'){
        this.falhaNoPagamentoOnline = true;
        this.mensagemFalhaPagamento = this.activatedRoute.snapshot.queryParams.autherr || 'Autenticação falhou';
      }
    }

    this.inicieMonitoramentoPedido();

  }


  private inicieMonitoramentoPedido() {
    const trintaSegundos = 1000 * 30;
    const cicoSegundos = 1000 * 5;
    const tempomonitoramento = this.retorno3ds ? cicoSegundos : trintaSegundos;

    this.timerPedidos =  timer(0, tempomonitoramento  ).subscribe( () => {
       this.obtenhaPedidosAtualizado()
    });

  }

  private obtenhaPedidosAtualizado(cb: any = null) {
    if(this.tentandoNovoPagamento || this.buscandoPedido) return;

    this.buscandoPedido = true;
    this.pedidosService.monitorePagamentoOnline(this.guid).then( (resultado: any) => {
      this.buscandoPedido = false;
      let pedido = resultado.pedido

      this.carregou = true;
      this.pedido = pedido || {};
      this.pagamento = resultado.pagamento || {};
      this.avaliacao = resultado.avaliacao;

      if(this.telaPix)
        this.telaPix.atualizouPagamento(this.pedido, this.pagamento)

      if(pedido.aguardandoTokenizar != null)
        this.aguardandoTokenizar = pedido.aguardandoTokenizar ;


      if(this.pagamento.formaDePagamento && this.pagamento.formaDePagamento.pix )
        this.titulo = `Pague R$ ${pedido.total.toFixed(2).replace('.', ',')} via PIX.`

      if(pedido.pago){
        let pedidoLocal = this.carrinhoService.obtenhaPedido();
        if(pedidoLocal.guid === this.guid)
          this.carrinhoService.limpePedido();
        this.foiPago = true;
        this.aguardandoTokenizar = false;
      }
      else
        this.foiPago = false

      this.falhaNoPagamentoOnline =  this.pagamento.falhaNoPagamentoOnline
      this.mensagemFalhaPagamento =  this.pagamento.mensagemFalhaPagamento
      this.foiReembolsado =  this.pagamento.foiReembolsado

      if(this.pagamento.urlAutenticar && !this.retorno3ds){
        this.aguardandoTokenizar = false;
        this.autenticarPagamento = true;
        this.open3DSDialog(this.pagamento.urlAutenticar);
      }

      if(pedido.finalizado)
        this.pareMonitoramento();

      if(this.pedido.horarioEntregaAgendada) {

        this.textoHorarioEntregaAgendada = moment(this.pedido.horarioEntregaAgendada)
          .utcOffset("-0" + (this.empresa.fusoHorario * -1) + "00").format("DD/MM/YYYY [às] HH:mm:ss")
      }

      if(cb)   cb();
    }).catch((err) => {
      this.buscandoPedido = false;
    })
  }

  ngOnDestroy(){
   this.pareMonitoramento()
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu() {
    return true;
  }

  cancelePedidoAguardando() {
    this.cancelando = true;

    this.pedidosService.cancelePedido(this.pedido).then( (resposta) => {
      this.cancelando = false;
      this.pedido = resposta;
      this.carrinhoService.limpePedido();

    }).catch((erro: any) => {
      this.cancelando = false;
      alert(erro)
    })
  }


  private pareMonitoramento() {
    if(this.timerPedidos)
      this.timerPedidos.unsubscribe();
  }

  gerePix(){
    this.tentandoNovoPagamento = true;

    this.pedidosService.tentePagamentoPix({codigo: this.pedido.codigo}).then((resposta: any) => {
      this.tentandoNovoPagamento = false;
      if(!resposta.falhaNoPagamentoOnline) {
        this.obtenhaPedidosAtualizado(() => { })
      } else  {
        this.falhaNoPagamentoOnline =  resposta.falhaNoPagamentoOnline
        this.mensagemFalhaPagamento = resposta.mensagemFalhaPagamento
      }
    }).catch( erro => {
      this.tentandoNovoPagamento = false;
      this.falhaNoPagamentoOnline = true;
      this.mensagemFalhaPagamento = erro;
    })
  }

  open3DSDialog(url3DS: string): void {
    if( this.dialogRefAutenticar) return;

    let dados: any = {
         title: 'Autenticação 3D Secure Cartão',
        content: ThreeDSDialogComponent,
        width: 600,
        height: 300
    }

    if( window.innerWidth < 800 )
      dados.width = '95%';

    this.dialogRefAutenticar = this.dialogService.open(dados);

    // Set the URL in the dialog component instance
    const dialogInstance =  this.dialogRefAutenticar.content.instance as ThreeDSDialogComponent;

    dialogInstance.setUrl(url3DS)

    this.dialogRefAutenticar.result.subscribe((result: any) => {
      this.dialogRefAutenticar = null
      if(result && result.retornou)
        this.obtenhaPedidosAtualizado()
    })
  }

  abrirPopupNovoPagamento() {
    const windowRef = TelaCartaoDeCreditoComponent.abraComoPopup(this.dialogService, false, this.pedido, this.pagamento, this.dadosCartao);
    this.telaPagamento = windowRef.content.instance;

    windowRef.result.subscribe((result: any) => {
      if(result && (result instanceof DadosCartao)){
        delete this.falhaNoPagamentoOnline;
        delete this.retorno3ds;
        this.tentandoNovoPagamento = true;

        this.pedidosService.tentePagamentoCartao({codigo: this.pedido.codigo
          , pagamentos: [{
            formaDePagamento: this.pagamento.formaDePagamento ,
            dadosCartao:  (result as DadosCartao).outroSemDadosSensitivos()
          }]}).then((resposta: any) => {
          this.tentandoNovoPagamento = false;
          if(!resposta.falhaNoPagamentoOnline) {
            this.obtenhaPedidosAtualizado(() => { })
          } else  {
            this.falhaNoPagamentoOnline =  resposta.falhaNoPagamentoOnline
            this.mensagemFalhaPagamento = resposta.mensagemFalhaPagamento
          }
        }).catch( erro => {
          this.tentandoNovoPagamento = false;
          this.falhaNoPagamentoOnline = true;
          this.mensagemFalhaPagamento = erro;
        })
      }
    });

  }

  obtenhaDescricaoPromocoes() {
    return this.pedido.promocoesAplicadas.map((promocaoAplicada) => promocaoAplicada.promocao.descricao ).join(",")
  }

  deveTerBordas() {
    return true;
  }


  deveExibirBannerTema() {
    return false;
  }

}

export enum FormaDeEntrega {
  RECEBER_EM_CASA = 'Receber em casa',
  RETIRAR = 'Retirar'
}
