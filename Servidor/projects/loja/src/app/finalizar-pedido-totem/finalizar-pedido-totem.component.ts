import {AfterViewInit, Component, ElementRef, Host, OnInit, Optional, ViewChild} from '@angular/core';
import {PedidosService} from "../../services/pedidos.service";
import {ConstantsService} from "../../services/ConstantsService";
import {CarrinhoService} from "../../services/carrinho.service";
import {ClienteService} from "../../services/cliente.service";
import {NgForm} from "@angular/forms";
import {DominiosService} from "../../services/dominios.service";
import {ITela} from "../../objeto/ITela";
import { PaginaComponent } from '../../pagina/pagina.component';

@Component({
  selector: 'app-finalizar-pedido-totem',
  templateUrl: './finalizar-pedido-totem.component.html',
  styleUrls: ['./finalizar-pedido-totem.component.scss']
})
export class FinalizarPedidoTotemComponent implements OnInit, ITela, AfterViewInit {
  @ViewChild('nomeInput') nomeInput: ElementRef;

  enviandoPedido = false;
  pedido: any = null;
  empresa: any = null;
  msgErro = '';
  dadosPedidoTotem = {
    nome: '',
    telefone: '',
    comanda: ''
  };
  sucesso = false;
  phoneMask = "(00) 00000-0000";
  //declara o form
  @ViewChild('frmPedidoTotem', {static: true}) frmPedidoTotem: NgForm;

  constructor(@Host() @Optional() private parent: PaginaComponent, private pedidosService: PedidosService, private constantsService: ConstantsService,
              private carrinhoService: CarrinhoService, private clienteService: ClienteService,
              private dominiosService: DominiosService) { }

  ngOnInit(): void {
    this.constantsService.empresa$.subscribe(data => {
      if (data) {
        this.empresa = data;
      }

      this.pedido = this.carrinhoService.obtenhaPedido();
    });
  }

  ngAfterViewInit() {
    this.focarEScrollParaNome();
  }

  focarEScrollParaNome() {
    window.onload = () => {
    var imagemPrincipal = document.getElementById('imagemPrincipal');

    if (this.nomeInput && this.nomeInput.nativeElement) {
        setTimeout(() => {
          //this.nomeInput.nativeElement.focus();
          document.getElementById('nome').scrollIntoView();
        }, 0);
    }
    };
  }

  onSubmit($event: any) {
    if( !this.frmPedidoTotem.valid ) {
      return;
    }

    this.enviandoPedido = true;
    this.pedido.origem = 'totem';

    this.clienteService.obtenhaMesaPorNome(this.dadosPedidoTotem.comanda).then((respMesa: any) => {
      if( !respMesa ) {
        this.msgErro = 'Código da comanda não existe!. Verifique por favor o número informado.';
        this.enviandoPedido = false;
        return;
      }

      this.pedido.mesa = respMesa;
      this.pedido.entrega.formaDeEntrega = 'Comer no local';
      this.pedido.contato = {
        nome: this.dadosPedidoTotem.nome,
        telefone: this.dadosPedidoTotem.telefone
      };

      this.pedidosService.salvePedido(this.pedido.obtenhaDadosEnvio(this.empresa)).then((resposta) => {
        this.pedido.codigo = resposta.codigo;
        this.pedido.guid = resposta.guid;
        this.enviandoPedido = false;
        this.sucesso = true;

        setTimeout(() => {
          this.fecharTelaPedido();
        }, 5000);
      }).catch( (erro) => {
        this.enviandoPedido = false;
        this.msgErro = erro;
      });
    });
  }

  informouTelefone() {

  }

  fecharTelaPedido() {
    localStorage.clear();
    window.location.href = this.dominiosService.obtenhaUrlHome() + "?tema=quiosque";
  }

  deveExibirBannerTema() {
    return true;
  }

  deveExibirMenu() {
    return true;
  }

  deveExibirTopo() {
    return false;
  }

  deveTerBordas() {
    return true;
  }
}
