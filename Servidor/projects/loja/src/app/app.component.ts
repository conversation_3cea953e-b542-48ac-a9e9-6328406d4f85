import {Component, OnInit, ViewContainerRef} from '@angular/core';
import {ActivatedRoute, NavigationEnd, Router} from "@angular/router";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {NgxSmartBannerService} from "@netcreaties/ngx-smart-banner";
import {CookieService} from "ngx-cookie-service";
import {ConstantsService} from "../services/ConstantsService";
import {Platform} from '@angular/cdk/platform';
import {CarrinhoService} from "../services/carrinho.service";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'loja';
  exibindoBanner = false;
  possuiApp: boolean;
  empresa: any;

  constructor(private router: Router, private autorizacao: AutorizacaoLojaService,
              private readonly ngxSmartBannerService: NgxSmartBannerService,
              private readonly viewContainerRef: ViewContainerRef, private activatedRoute: ActivatedRoute,
              private readonly cookieService: CookieService,
              private readonly constantsService: ConstantsService,  private carrinhoService: CarrinhoService,
              private readonly platform: Platform) {
    this.router.events.subscribe(event => {
      if (event instanceof NavigationEnd && (<any>window).ga ) {
        (<any>window).ga('set', 'page', event.urlAfterRedirects);
        (<any>window).ga('send', 'pageview');

        if((<any>window).analytics) {
          (<any>window).ga('custom.set', 'page', event.urlAfterRedirects);
          (<any>window).ga('custom.send', 'pageview');
        }

        if((<any>window).dataLayer) {
          (<any>window).dataLayer.push({event: 'pageview'});
        }
      }
    });

    let deferredPrompt;

    this.carregueEmpresaExibaBanner();
    window.addEventListener('beforeinstallprompt', (e) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      this.carregueEmpresaExibaBanner();
      //e.preventDefault();
      // Stash the event so it can be triggered later.
      deferredPrompt = e;
    });
  }

  carregueEmpresaExibaBanner() {
    this.constantsService.empresa$.subscribe( (empresa) => {
      if( !empresa )    return;

      this.ajusteVariaveisTema(empresa);

      if( empresa.tema === 'quiosque' ) {
        return;
      }

      this.empresa = empresa;
      window['tema'] = empresa.tema;

      this.constantsService.moduloApp$.subscribe(possuiApp => {
        this.possuiApp = possuiApp;

        if (this.possuiApp) {
          this.exibaBanner();
        }
      });
    });
  }

  exibaBanner() {
    if( !this.empresa.appIos && this.platform.IOS ) { //não tem app para ios
      return;
    }

    if( window['Flutter'] || window['flutter_inappwebview'] ) { //dentro do app não deve exibir o banner
      return;
    }

    // Não exibir o banner se estiver na tela de tablet-pedidos
    if (this.router.url.includes('pedido-tablet')) {
      return;
    }

    this.cookieService.delete('smartbanner_closed');

    this.ngxSmartBannerService.onOpen.subscribe( () => {
      this.exibindoBanner = true;
    });
    this.ngxSmartBannerService.onClose.subscribe( () => {
      this.exibindoBanner = false;
    });
    const fotoLogo = `https://${this.empresa.dominio}.meucardapio.ai/images/empresa/` + this.empresa?.logo;

    this.ngxSmartBannerService.initialize({
      viewContainerRef: this.viewContainerRef,
      daysHidden: 0,
      daysReminder: 0,
      closeLabel: 'Fechar',
      author: this.empresa.nome,
      title: 'Baixe agora o app',
      price: 'Gratuito',
      priceSuffix: {
        ios: ' - na Play Store',
        android: ' - no Google Play'
      },
      icon: {
        ios: fotoLogo,
        android: fotoLogo
      },
      rating: {
        ios: 5,
        android: 5
      },
      buttonLabel: 'Download',
      buttonUrl: {
        android: 'https://play.google.com/store/apps/details?id=ai.meucardapio.' + this.empresa.dominio,
        ios: this.empresa.appIos
      }
    });
  }

  private extractHmFromUrl(url: string): string | null {
    const matches = url.match(/\/local\/([a-zA-Z0-9]+)/);
    return matches ? matches[1] : null;
  }


  ngOnInit(): void {

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if(!this.constantsService.carregou){
          const hm = this.extractHmFromUrl(event.urlAfterRedirects);

          this.inicializePedido(hm).then( (pedido: any) => {
            let deMesa = (hm  != null || (pedido && pedido.mesa  != null ))

            this.constantsService.recarregueEmpresa( deMesa)
          });

        }
      }
    });

    if( this.estahNoApp() ) {
      this.autorizacao.estaLogado().then( (resposta: any) => {
        if(resposta.logado) {
          const usuario = this.autorizacao.getUsuario();

          const mensagem = {
            tipo: 'usuario',
            usuario: usuario
          }

          this.notifiqueAppFlutter(mensagem);
        } else {
          const mensagem = {
            tipo: 'carregou'
          }
        }
      })
    }
  }

  private inicializePedido(hashMesa: string) {
    return new Promise((resolve) => {
      if(hashMesa) {
        this.carrinhoService.obtenhaMesa(hashMesa).then((mesa: any) => {
          if(!mesa){
            resolve( this.carrinhoService.obtenhaPedido({ expirada: true }));
          } else {
            mesa.hash  = hashMesa
            resolve( this.carrinhoService.obtenhaPedido(mesa));
          }
        })
      } else {
        resolve(this.carrinhoService.obtenhaPedido(    ));
      }
    })
  }

  estahNoApp() {
    return window['Flutter'] || window['flutter_inappwebview'];
  }

  notifiqueAppFlutter(mensagem: any) {
    if( window['Flutter'] ) {
      window['Flutter'].postMessage(JSON.stringify(mensagem));
    }

    if( window['flutter_inappwebview'] ) {
      window['flutter_inappwebview'].logou(mensagem);
    }
  }

  ajusteVariaveisTema(empresa: any) {
      if( empresa.temaPersonalizado ) {
        document.documentElement.style.setProperty('--cor-fundo', empresa.temaPersonalizado.corFundo || '#f7f8f8');
        document.documentElement.style.setProperty('--cor-texto', empresa.temaPersonalizado.corTextoSecundaria || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-topo', empresa.temaPersonalizado.corTextoTopo || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-primaria', empresa.temaPersonalizado.corTextoPrimaria || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-secundaria', empresa.temaPersonalizado.corTextoSecundaria || 'inherit');
        document.documentElement.style.setProperty('--cor-texto-muted', empresa.temaPersonalizado.corTexto ? `color-mix(in srgb, ${empresa.temaPersonalizado.corTexto} 70%, black)` : '#6c757d');
        document.documentElement.style.setProperty('--cor-botao', empresa.temaPersonalizado.corBotao || '#007bff');
        document.documentElement.style.setProperty('--cor-texto-botao', empresa.temaPersonalizado.corTextoBotao || '#007bff');
        document.documentElement.style.setProperty('--cor-preco-adicional', empresa.temaPersonalizado.corPrecoAdicional || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-texto-preco-adicional', empresa.temaPersonalizado.corTextoPrecoAdicional || '#FFFFFF');
        document.documentElement.style.setProperty('--cor-fundo-site', empresa.temaPersonalizado.corFundoDoSite || '#f7f8f8');
        document.documentElement.style.setProperty('--cor-borda', empresa.temaPersonalizado.corBorda || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-destaque', empresa.temaPersonalizado.corDestaque || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-hover', empresa.temaPersonalizado.corHover || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-preco', empresa.temaPersonalizado.corPreco || '#e2e3e3');
        document.documentElement.style.setProperty('--cor-texto-rodape', empresa.temaPersonalizado.corTextoRodape || empresa.temaPersonalizado.corTextoFundo || 'white');
        document.documentElement.style.setProperty('--cor-fundo-rodape', empresa.temaPersonalizado.corFundoRodape || empresa.temaPersonalizado.corFundo || '#222');
        document.documentElement.style.setProperty('--cor-borda-rodape', empresa.temaPersonalizado.corBordaRodape || empresa.temaPersonalizado.corBorda || '#dee2e6');
      }
  }
}
