<kendo-dropdownlist
  [data]="dados"
  [textField]="'alpha2'"
  [valueField]="'phone'"
  [(ngModel)]="selectedCountry"
  [filterable]="true"
  (filterChange)="handleFilter($event)"
  [popupSettings]="{
        width: 160,
        animate: false
      }"
  style="width: 115px;"
  (valueChange)="onCountryChange($event)">
  <ng-template kendoDropDownListValueTemplate  let-dataItem>
    <div class="country-item" *ngIf="dataItem">
      <img [src]="dataItem.flag" alt="{{dataItem.alpha2}}" width="20" height="20" />
        ({{ dataItem.phone }})
    </div>
  </ng-template>
  <ng-template kendoDropDownListItemTemplate let-dataItem>
    <div class="country-item" *ngIf="dataItem">
      <img [src]="dataItem.flag" alt="{{dataItem.alpha2}}" width="20" height="20" />
      {{ dataItem.alpha2 }} ({{ dataItem.phone }})
    </div>
  </ng-template>
</kendo-dropdownlist>
