import {AfterViewInit, Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {ConstantsService} from "../../services/ConstantsService";
import {ProdutoService} from "../../services/produto.service";
import {DominiosService} from "../../services/dominios.service";
import {ITela} from "../../objeto/ITela";
import {SiteProdutoComponent} from "../../site-produto/site-produto.component";
import {SiteMontarpizzaComponent} from "../../site-montarpizza/site-montarpizza.component";
import {DialogService} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";
import {SharedDataService} from "../../services/shared.data.service";

@Component({
  selector: 'app-tela-busca-ecommerce',

  templateUrl: './tela-busca-ecommerce.component.html',
  styleUrls: ['./tela-busca-ecommerce.component.scss']
})
export class TelaBuscaEcommerceComponent implements OnInit, ITela, AfterViewInit  {
  categoria: any;
  categoriaTopo: any;
  subCategorias: any = [];
  categorias: any;
  todasCategorias: any
  produtos: any = [];
  window: any;
  isMobile = false;
  items: any = [];
  buscando = true;
  buscandoScroll = false;
  buscouTodos = false;
  private   TOTALPORPAGINA = 3 * 10
  filtro = ''
  private timerBusca;
  todas = false;
  constructor(private router: Router, private constantsService: ConstantsService,
              public sharedDataService: SharedDataService,
              private produtoService: ProdutoService, private dominiosService: DominiosService,
              private activatedRoute: ActivatedRoute,  private dialogService: DialogService,
              private location: Location) { }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe( async (params) => {

      if( window.history.state.categorias ) {
        this.categorias =  window.history.state.categorias;
      } else {
        this.categorias = await this.produtoService.listeCategoriasMegamenu();
      }

      this.todas =  params.categoria === 'todas';

      this.setTodasCategorias(!this.todas ? Number( params.categoria) : null);
      this.calculeBreadCrumbs();


      if( window.history.state.produtos){
        this.buscando = false;
        this.buscandoScroll = false;
        this.produtos = window.history.state.produtos
        this.buscouTodos = true
      } else {
        this.busqueProdutos();
      }

      });
  }

  verTodos($event){
    this.busqueProdutos(0);

    return false;
  }

  onScroll() {
    if(this.buscandoScroll || this.buscouTodos) return;

    this.buscandoScroll = true;

    this.busqueProdutos(this.produtos.length + 1,  this.filtro);
  }

  busqueProdutos(inicio: number = 0, texto: string = null){
    this.buscando = true;

    if(this.categoria){
      this.produtoService.listeDaCategoria(this.categoria.id, inicio, this.TOTALPORPAGINA, texto).then( (resposta: any) => {
        let produtos: any = resposta.produtos;

        this.buscando = false;
        this.buscandoScroll = false;
        this.produtos = inicio === 0 ? produtos : [...this.produtos, ...produtos];
        this.buscouTodos = produtos.length === 0;
      })
    } else {
      this.produtoService.listeDaVitrine(inicio, this.TOTALPORPAGINA, texto).then( (resposta: any) => {
        let produtos: any = resposta.produtos;

        this.buscando = false;
        this.buscandoScroll = false;
        this.produtos = inicio === 0 ? produtos : [...this.produtos, ...produtos];
        this.buscouTodos = produtos.length === 0;
      })
    }


  }

  navegueParaCategoria(categoria: any) {

    this.sharedDataService.exibirMenuMobile = false;
    this.produtos = []

    this.router.navigateByUrl('/busca/ecommerce/' + categoria.id,
      {state: { categoria: categoria, categorias: this.categorias }  });

    return false;
  }

  calculeBreadCrumbs() {

    this.items = [{ icon: "home", text: 'loja', path: 'loja' }]

    if(this.categoria){

      let categoriasAcimas = this.obtenhaCategoriasAcima(this.categoria);

      let items: any = categoriasAcimas.map((cat: any) => {
        return {  text:  cat.nome, title:  cat.nome, id: cat.id}
      })

      items.push( { text:  this.categoria.nome, title:  this.categoria.nome})

      this.items.push( ...items)
    }
  }


  exibirUnidade(produto: any) {
    return produto.tipoDeVenda && produto.tipoDeVenda === 'Peso'
  }


  abraDetalhesProduto(produto: any) {
    if(!produto.montar){
      this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto);
    } else {
      this.window =  SiteMontarpizzaComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto);
    }

  }

  public onItemClick(item: any): void {
    const selectedItemIndex = this.items.findIndex((i) => i.text === item.text);
    let itens = this.items
      .slice(0, selectedItemIndex + 1);

    let url = [];
    if(item.path ){ //home
      url = [item.path]
    } else {
      url = ['/busca/ecommerce', item.id]
    }

    this.router.navigate(url);
  }

  ngAfterViewInit(): void {
  }

  obtenhaCategoriasAcima(categoria: any){
    let categoriasAcima = []

    let categoriaAcima = this.obtenhaCategoriaAcima(categoria)

    if(categoriaAcima){
      categoriasAcima.push(categoriaAcima)

      if(categoriaAcima.categoriaPai)
        categoriasAcima.unshift(categoriaAcima.categoriaPai)

    }

    return categoriasAcima;

  }


  obtenhaCategoriaAcima(categoria: any): any{
    if(!categoria || !categoria.categoriaPai) return null

    let caregoriaAcima: any = this.todasCategorias.find((_categoria: any) => _categoria.id === categoria.categoriaPai.id)

    return caregoriaAcima

  }


  obtenhaCategoriaTopo(categoria: any): any{
    let categoriaAcima = this.obtenhaCategoriaAcima(categoria)

    if(categoriaAcima && categoriaAcima.categoriaPai)
      return  this.obtenhaCategoriaTopo(categoriaAcima.categoriaPai)


    return categoriaAcima ? categoriaAcima : categoria;
  }

  private setTodasCategorias(idCategoria: number) {
    this.todasCategorias = [];

    this.categorias.forEach((categoriaPai: any) => {
      this.adicioneCategoria(categoriaPai);

      for(let i = 0; i < categoriaPai.subcategorias.length; i++ ){
        let catnivel2 = categoriaPai.subcategorias[i];
        this.adicioneCategoria(catnivel2)

        for(let j = 0; j < catnivel2.subcategorias.length; j++ ){
          let catnivel3 = catnivel2.subcategorias[j];
          this.adicioneCategoria(catnivel3)
        }
      }
    })

    if(idCategoria){
      this.categoria =  this.todasCategorias.find((cat: any) => cat.id === Number( idCategoria));

      let categoriaTopo = this.obtenhaCategoriaTopo(this.categoria);

      this.categoriaTopo = this.categorias.find( cat => cat.id === categoriaTopo.id);
    }

  }

  adicioneCategoria(categoria){
    let copia = Object.assign({}, categoria)

    delete copia.subcategorias;

    this.todasCategorias.push(copia)
  }


  deveExibirMenu() {
    return true;
  }

  deveExibirTopo() {
    return true;
  }

  deveTerBordas() {
    return true;
  }

  exibirMenuDessaCategoria(subcategoria: any) {
    if(this.categoria.nivel < subcategoria.nivel) return true;

    if(this.categoria.id === subcategoria.id) return  true;


    return this.categoria.categoriaPai && subcategoria.id === this.categoria.categoriaPai.id
  }

  fecheMenuCategorias() {
    this.sharedDataService.exibirMenuMobile = false;
    return false;
  }

  onChange(value: any) {
    if(this.buscando) return;
    this.filtro = value;
    if(value && value.length >= 3){

      if(this.timerBusca) clearTimeout(this.timerBusca);

      this.timerBusca = setTimeout( () => {
        this.busqueProdutos(0, value);
      }, 1000);

    }
  }

  deveExibirBannerTema() {
    return false;
  }
}
