<app-header-loja [titulo]="'Código Validação'" [retorno]="this.retorno" *ngIf="!this.modal"></app-header-loja>
<app-header-tela [titulo]="'Código Validação'" [exibirFechar]="true" (fechou)="fecheTela()" *ngIf="this.modal"></app-header-tela>

<form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="gereCodigoValidacaoWhatsapp()" class="mt-3"    >

  <div class="form-group mb-2 col">
    <label for="telefone" class="label-disabled"> Telefone</label>
    <div class="input-group ">
      <div style="display: flex">
        <app-seletor-codigo-pais
          [selectedCountryCode]="contato.codigoPais"
          (phoneMaskChange)="onPhoneMaskChange($event)"
          (selectedCountryCodeChange)="onCountrySelected($event)"></app-seletor-codigo-pais>


        <kendo-maskedtextbox  id="telefone" name="telefone"  #telefone="ngModel" [mask]="phoneMask"
                              placeholder="Telefone com DDD" class="form-control my-custom-maskedtextbox "
                              [disabled]="validarCodigo"
                              #kendoMaskedTextBox="kendoMaskedTextBox"  autocomplete="off"
                              [(ngModel)]="contato.telefone"   required>
        </kendo-maskedtextbox>



      </div>
      <div class="invalid-feedback">
        <p *ngIf="telefone.errors?.required">Informe o número do telefone</p>
      </div>
    </div>

  </div>

  <div *ngIf="validarCodigo">
    <p class="text-center text-dark">Digite o código de  5 digitos  enviado para o <b>WhastApp</b></p>

    <div class="input-group mb-2 col">

      <input class="form-control codigo-validacao" type="text" minlength="5" mask="0-0-0-0-0" [disabled]="processando"
             id="codigo" name="codigo"  #codigo="ngModel" (ngModelChange)="alterouCodigo($event)"
             [(ngModel)]="contato.codigo" placeholder="0-0-0-0-0" required  appAutoFocus>


      <div class="invalid-feedback" >
        <p *ngIf="codigo.errors?.required && contato.codigo">Informe o código de validação de 5 digitos</p>
      </div>
    </div>
  </div>


  <div class="text-danger mt-2 mb-2  text-center" role="alert" *ngIf="erro"  >
    <b> {{erro}}</b>
  </div>

  <div class="text-success mt-2 mb-2  text-center" role="alert" *ngIf="codigoValido"  >
    <b> Codigo validado!</b>
  </div>

  <div class="mt-3 mb-3 col"  *ngIf="!validarCodigo" >
    <button class="btn btn-blue btn-block "  type="submit"  [disabled]="processando" >
      <i class="k-icon k-i-loading mr-1" *ngIf="processando"></i>
      Validar Telefone </button>

  </div>

  <div *ngIf="validarCodigo" [hidden]="countdownSeconds === 0">
    <div class="timer mt-2">
      <h1>{{ formattedTime }}</h1>
    </div>

    <ng-container *ngIf="countdownSeconds===0 && validarCodigo">
      <div class="mt-3 mb-3 col" >
        <button class="btn btn-blue btn-block "  type="submit"  [disabled]="processando || countdownSeconds > 0" >
          <i class="k-icon k-i-loading mr-1" *ngIf="processando"></i>
          Gerar novo codigo
        </button>

      </div>

      <div class="mt-3 mb-3 col"  *ngIf="this.empresa?.numeroWhatsapp">
        <button class="btn btn-success waves-effect btn-block"  (click)="falarComLoja($event)">
           Não recebi codigo, falar com a loja  <i class="fab fa-whatsapp fa-lg ml-1" ></i>
        </button>
      </div>

    </ng-container>


    <!--<a class="text-center d-block mt-1 text-blue " href="" (click)="informarNovoTelefone($event)" [hidden]="contato.id || modal" >
      Informar outro telefone</a> -->


  </div>

</form>

