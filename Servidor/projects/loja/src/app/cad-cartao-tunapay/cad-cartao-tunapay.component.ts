import {ApplicationRef, Component, ElementRef, Input, OnInit, ViewChild} from '@angular/core';
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {DadosCartao} from "../../objeto/DadosCartao";
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {FormGroup} from "@angular/forms";
import {NgForm} from "@angular/forms";
import {TunapayService} from "../../services/tunapay.service";
import {CadCartaoPadrao} from "../../objeto/CadCartaoPadrao";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {TunapayScript} from "../../loja-pagamento/formas-pagamentos/tunapay.script";

declare var Tuna: any;
declare var google: any;

@Component({
  selector: 'app-cad-cartao-tunapay',
  templateUrl: './cad-cartao-tunapay.component.html',
  styleUrls: ['./cad-cartao-tunapay.component.scss']
})
export class CadCartaoTunapayComponent extends TunapayScript implements OnInit, ICartaoCreditoGateway {
  @Input() pedido: PedidoLoja;
  @ViewChild('formCartao') formCartao: NgForm;
  @ViewChild('formCartao', { read: ElementRef }) public frmElement: ElementRef;
  processando = false;
  erroCartao: string;
  msgErro: string;
  carregando3ds = false;
  constructor(public tunapayService: TunapayService, public app: ApplicationRef,
              private cadCartaoPadrao: CadCartaoPadrao, private autorizacaoService: AutorizacaoLojaService) {
    super(tunapayService, app, null)
  }

  ngOnInit() {
    let usuario = this.autorizacaoService.getUsuario();

    if(usuario && usuario.cpf) this.cartao.cpf = usuario.cpf
    if(usuario && usuario.email) this.cartao.email = usuario.email

    if(! this.cartao.nome) this.cartao.nome = ''
    if(! this.cartao.cpf) this.cartao.cpf = ''


    this.inicializeTuna(this.pedido.guid).then((err) => {
      if(!err){
        this.definiuSessao = true;
        this.processando = false;
      } else {
        this.erroInicializacao = 'Não foi possível criar sessão de pagamento.'
      }
    })
  }

  ehValido(): any {
    return this.cadCartaoPadrao.ehValido(this.formCartao, this.frmElement);
  }

  exibaCartao(dadosCartao: DadosCartao) {
    if (dadosCartao) {
      this.cartao = new DadosCartao();

      Object.assign(this.cartao, dadosCartao);
    }
  }

  async crieTokenCartao(endereco: any): Promise<DadosCartao> {
    return new Promise( async (resolve, reject) => {
      try {
        this.processando = true;
        this.erroCartao = null;

        const cardData = {
          cardHolderName: this.cartao.nome,
          cardNumber: this.cartao.numero.replace(/\s/g, ''),
          expirationMonth: this.cartao.validade.getMonth() + 1,
          expirationYear: this.cartao.validade.getUTCFullYear(),
          cvv: this.cartao.cvv,
          singleUse: true,
          //cardHolderDocument: this.cartao.cpf.replace(/\D/g, '')
        };
        const tokenizator = this.tunaInstance.tokenizator();
        const response = await tokenizator.generate(cardData);

        this.cartao.token = response.token;
        this.cartao.bandeira = response.brand || '';
        this.cartao.ultimosNumeros = cardData.cardNumber.slice(-4);
        this.cartao.deviceInfo =  this.getDeviceInfo();
        this.cartao.descricaoPagamento = `${this.cartao.bandeira.toUpperCase().trim()} *** ${this.cartao.ultimosNumeros}`;
        resolve(this.cartao)
      } catch (erro) {
        this.erroCartao = erro.message || 'Erro ao processar cartão';
        reject(this.erroCartao)
      } finally {
        this.processando = false;
      }
    })
  }

  fecheMensagemErro(){

  }

  private getDeviceInfo() {
    const deviceInfo: any = {
      cookiesAccepted:  navigator.cookieEnabled,
      // @ts-ignore
      acceptLanguage: navigator.language || navigator.userLanguage,
      colorDepth: window.screen.colorDepth, // Profundidade de cor da tela
      origin: 'WEBSITE', // Definido como 'BROWSER' por padrão
      javaEnabled: navigator.javaEnabled(), // Verifica se o Java está habilitado
      screenHeight: window.screen.height, // Altura da tela
      screenWidth: window.screen.width, // Largura da tela
      timeDifference: new Date().getTimezoneOffset() ,  // Fuso horário em horas
      javaScriptEnabled: true
    };

    return deviceInfo;
  }
}
