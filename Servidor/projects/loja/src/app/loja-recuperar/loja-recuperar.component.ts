import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ITela} from "../../objeto/ITela";
import {NgForm} from "@angular/forms";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";
import {DialogService} from "@progress/kendo-angular-dialog";
import {PopupUtils} from "../../objeto/PopupUtils";
import {DialogRef} from "@progress/kendo-angular-dialog";

@Component({
  selector: 'app-loja-recuperar',
  templateUrl: './loja-recuperar.component.html',
  styleUrls: ['./loja-recuperar.component.scss']
})
export class LojaRecuperarComponent implements OnInit, ITela {
  @ViewChild('frm')  frm: NgForm;
  @ViewChild('frmv')  frmv: NgForm;
  @ViewChild('frms')  frms: NgForm;
  @ViewChild('codigoEle', {static: false})  codigoEle: ElementRef;
  @ViewChild('senhaEle', {static: false})  senhaEle: ElementRef;
  model: any = {};
  aguardeProcessar: any;
  erro: string;
  exibirSenha: any;
  window: any
  abriuComoModal = false;
  constructor(private autorizacao: AutorizacaoLojaService, private router: Router,
              private dominiosService: DominiosService,
              private activatedRoute: ActivatedRoute) { }

  static abraComoPopup(router,  location, activatedRoute, dialogService: DialogService){
    let dimensao = PopupUtils.calculeAlturaLargura(false)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: LojaRecuperarComponent,
      minWidth: 200,
      width: dimensao.largura,
      height: 500
    });

    let tela: LojaRecuperarComponent = windowRef.content.instance;

    tela.setModal(windowRef);

    PopupUtils.abraJanela(router,  location, activatedRoute, windowRef, {});

  }
  ngOnInit(): void {
    this.model.telefone = window.history.state.telefone;
    let token = this.activatedRoute.snapshot.params.token;

    if(!token){
      this.informarTelefone();
    } else {
      this.autorizacao.obtenhaToken(token).then( resp => {
        this.model.email = resp.email;
        this.informarCodigo(token)
      }).catch(erro => {   this.informarTelefone();  this.erro = erro})
    }
  }

  informarTelefone(){
    this.model.passo = 'telefone';
  }

  informarCodigo(token){
     this.model.passo = 'codigo';
     this.model.token = token;
     setTimeout(() => {
       this.codigoEle.nativeElement.focus();
     }, 500);
  }

  redefinirSenha(){
    this.model.passo = 'senha';
    setTimeout(() => {
      this.senhaEle.nativeElement.focus();
    }, 500);
  }

  onEnviarVerificacao() {
    delete this.erro;
    if(this.frm.valid){
      this.aguardeProcessar = true;
      this.autorizacao.envieEmailRecuperacao(this.model.telefone).then( (resposta) => {
        let token = resposta.token
        this.model.email = this.mascareEmail(resposta.email)
        this.aguardeProcessar = false;
        this.informarCodigo(token);
      }).catch(erro => {      this.aguardeProcessar = false; this.erro = erro})

    }
  }

  mascareEmail(email) {
    // Obtém os últimos 12 caracteres do email (ou 6, se o email tiver menos que 18 caracteres)
    let lastChars;
    if (email.length < 18) {
      lastChars = email.slice(-6);
    } else {
      lastChars = email.slice(-12);
    }

    // Gera uma string de * com o número de caracteres iniciais (12 ou 6, dependendo do tamanho do email)
    let mask;
    if (email.length < 18) {
      mask = '*'.repeat(email.length - 6);
    } else {
      mask = '*'.repeat(email.length - 12);
    }

    // Retorna a máscara + os últimos caracteres
    return mask + lastChars;
  }

  alterouCodigo(codigo) {
    if(this.frmv.controls.codigo.valid){
      delete this.erro;
      this.aguardeProcessar  = true;
      this.autorizacao.verifiqueCodigoConfirmacao(this.model.token, codigo).then( resp => {
        this.aguardeProcessar = false;
        this.redefinirSenha();
      }).catch(erro => {
        this.aguardeProcessar = false;
        this.erro = erro;
      })
    }
  }

  onRedefinir() {
    delete this.erro;
    if(this.frms.valid){
      this.autorizacao.troqueSenha(this.model.token, this.model.senha).then( resp => {

        if(!this.abriuComoModal){
          this.dominiosService.navegueParaUrl('login', { telefone: this.model.telefone})
        } else {
          this.fecheTela();
        }
      }).catch(erro => {
        this.aguardeProcessar = false;
        this.erro = erro;
      })
    } else {
      alert('Formulario invalido')
      console.log(this.frms)
    }
  }

  exibirSenhaTela() {
    this.exibirSenha = ! this.exibirSenha ;
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return true;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  setModal(window){
    this.window = window;
    this.abriuComoModal = true;
  }

  fecheTela() {
    this.window.close();
  }
}
