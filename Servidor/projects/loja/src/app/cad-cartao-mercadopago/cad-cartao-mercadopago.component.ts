import {
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  NgZone,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import {NgForm} from "@angular/forms";
import {PedidoLoja} from "../../objeto/PedidoLoja";
import {DadosCartao} from "../../objeto/DadosCartao";
import {DOCUMENT} from "@angular/common";
import {IFormCartaoRespostaValido} from "../../objeto/IFormCartaoRespostaValido";
import {ICartaoCreditoGateway} from "../../objeto/ICartaoCreditoGateway";
import {MercadopagoService} from "../../services/mercadopago.service";
declare var $;
@Component({
  selector: 'app-cad-cartao-mercadopago',
  templateUrl: './cad-cartao-mercadopago.component.html',
  styleUrls: ['./cad-cartao-mercadopago.component.scss']
})
export class CadCartaoMercadopagoComponent implements OnInit, ICartaoCreditoGateway {
  @ViewChild('frm', { static: true})  frm: NgForm;
  @ViewChild('frm', { read: ElementRef }) public frmElement: ElementRef;
  @ViewChild('numeroElem', { read: ElementRef }) public numeroElem: ElementRef;
  @Input() parcelamento: any;
  @Input() pedido: PedidoLoja;
  @Output() onCriou = new EventEmitter();
  @Output() onGerouToken = new EventEmitter();
  empresa: any;
  processando: any;
  mensagemSucesso: any;
  mensagemErro: any;
  cartao: any = new DadosCartao();

  definiuSessao  = false;
  parcelas: any;
  paymentMethod: any
  timerVerificar: any;
  bandeiras = []
  Mercadopago: any;
  renderizou = false;
  constructor(private _renderer2: Renderer2, private ngZone: NgZone,
              private mercadopagoService: MercadopagoService,
              @Inject(DOCUMENT) private _document: Document, private _ngZone: NgZone) {
    this.cartao.numero = '';
    this.cartao.cvv = '';
    this.cartao.nome = '';
    this.cartao.cpf = '';
    this.cartao.validade = null;
    this.cartao.dataNascimento = null;
  }

  ngOnInit(): void {
    this.incluaScriptMercadoPago();
  }

  private incluaScriptMercadoPago() {
    let script = this._renderer2.createElement('script');
    script.type = 'text/javascript';
    script.src =  'https://sdk.mercadopago.com/js/v2';
    this._renderer2.appendChild(this._document.body, script);

    this.ativeMercadoPago();
  }

  private ativeMercadoPago() {
    if ((window as any).MercadoPago){
      this.definiuSessao = true;
      this.mercadopagoService.obtenhaPublicToken().then( async (token) => {
        // fazer request pergar no server
        try{
          this.Mercadopago = new (window as any).MercadoPago(token, {
            locale: 'pt-BR'});

          const bricksBuilder = this.Mercadopago.bricks();

          await this.renderCardPaymentBrick(bricksBuilder);
        } catch (error){
          console.error(error)
          this.mensagemErro = 'Ops! falha ao configurar scripts MercadoPago: ' + error.message
        }
      }).catch( (err) => {
        console.error(err)
        this.mensagemErro = 'Ops! naõ foi possivel iniciar sessão pagamento, feche e tente de novo.'
      })
    } else {
      setTimeout( () => { this.ativeMercadoPago()}, 500)
    }
  }

  async renderCardPaymentBrick(bricksBuilder: any){
    const settings = {
      initialization: {
        amount: 0, // total amount to be paid
        payer: {  }
      },
      customization: {
        paymentMethods: {
          minInstallments: 1,
          maxInstallments: 1,
        },
        visual: {
          style: {
            theme: 'bootstrap', // | 'dark' | 'bootstrap' | 'flat'
          },
          texts: {
            formSubmit: "Confimar cartão"
          }
        }
      },
      callbacks: {
        onReady: () => {
          this.renderizou = true;
        },
        onSubmit: (cardFormData) => {
          // callback called the user to click on the data submit button
          // example of sending the data collected by el Brick to your
          return new Promise((resolve, reject) => {
            console.log(cardFormData)
            this.cartao.token = cardFormData.token;
            this.cartao.bandeira = cardFormData.payment_method_id;
            this.cartao.numero = cardFormData.payment_method_id;
          //  this.cartao.nome = cardFormData.payer.name;
            this.cartao.tipoDoc = cardFormData.payer.identification.type;
            this.cartao.cpf = cardFormData.payer.identification.number;
            this.cartao.email = cardFormData.payer.email;
           // this.cartao.issuer_id = cardFormData.issuer_id;
            this.onGerouToken.emit(this.cartao)
          });
        },
        onError: (error) => {
          // callback called for all Brick error cases
          console.error(error);
        },
      },
    };

    if((this.pedido as any).cliente){ // pedido server
      let contato: any = (this.pedido as any).cliente;

      settings.initialization.amount = this.pedido.total;
      settings.initialization.payer = {
         cpf: contato.cpf,
         email: contato.email,
      }
    } else{ //pedido loja
      settings.initialization.amount = this.pedido.obtenhaValorAhPagar();
      settings.initialization.payer = {
        cpf: this.pedido.contato.cpf,
        email: this.pedido.contato.email,
      }
    }

    (window as  any).cardPaymentBrickController =
      await bricksBuilder.create('cardPayment', 'cardPaymentBrick_container', settings);

 }

  ehValido(): IFormCartaoRespostaValido {
    return {
      valido: true,
      controle: null
    }

  }

  crieTokenCartao(enderecoCobranca: any): Promise<DadosCartao> {
    if(this.cartao.token)
      return Promise.resolve(this.cartao);
    else
      return Promise.reject('Erro')
  }

  exibaCartao(dadosCartao: DadosCartao) {
    this.cartao = new DadosCartao();

    Object.assign(this.cartao, dadosCartao);
  }
}
