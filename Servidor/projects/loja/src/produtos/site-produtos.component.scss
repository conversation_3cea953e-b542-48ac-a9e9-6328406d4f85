$black:  rgb(31, 41, 55);
$cinza: rgb(107, 114, 128);

.faixa-fidelidade{
  width: 100%;
  padding: 10px;
  font-family: ui-sans-serif, system-ui, sans-serif,
  "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  color: $black;
  padding-bottom: 0;

  h5{
    color: var(--cor-texto-primaria);
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
  }

  .texto2{
    color: $cinza;
  }

  p{
    font-size: 16px;
  }
  .pontos{
    font-weight: 700;
    font-size: 36px;
    line-height:40px;
  }

  .header-grid{
    display: flex;
    justify-content: space-between;
    .link{
      display: flex;
      border: none;
      font-size: 15px;
      background-color: transparent;
      background-image: none;
      text-transform: none;
      color: $cinza !important;
      span{
        font-size: 22px;
        top: -7px;
        position: relative;
        left: 2px;
      }
    }
  }

  .grid{
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(2, minmax(0px, 1fr));
    .brinde{
      display: block;
      padding: 1rem;
      background-color: rgb(249 250 251);
      border-color: rgb(229 231 235);
      border-width: 1px;
      border-radius: 0.75rem;
      cursor: pointer;
      border-style: solid;
      >div{
        display: flex;
        .foto{
          overflow: hidden;
          width: 96px;
          margin-right: 10px;
        }
      }

      &.naodisponivel{
        .points-badge {
          background-color: #9e9e9e70;
          color: #000000a3;
          .points-icon path{
            fill: black !important;
          }
        }
        .foto{
          opacity: 0.5; /* Torna a imagem semitransparente */
          filter: grayscale(100%); /* Opcional: Deixa a imagem em preto e branco */
        }
      }
    }
  }

  .descricao{
    position: relative;
    h5{
      font-size: 15px;
    }
  }

  .points-badge {
    background-color: #FFB700;
    border-radius: 25px;
    padding: 5px 15px;
    max-width: 120px;

    z-index: 1;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;

    .points-icon{
      width: 16px;
      height: 16px;
      margin-right: 2px;
      top: -2px;
      position: relative;
      path{
        fill: #CB0A0A;
      }
    }

  }

}

.boxinfo{
  border: 1px solid var(--cor-borda);
  border-radius: 10px;
  padding: 10px;
  color: var(--cor-texto-primaria);
  background: var(--cor-fundo-elementos);

  a {
    color: var(--cor-destaque);
  }
}

.media .text-danger{
  color: #f1556c7a !important;
}

.capa_empresa {
  height: 200px;
  background-size: cover;
}

.capa_empresa.centralizada {
  z-index: 10;
  background-repeat: no-repeat;
  background-position-x: center;
  background-size: cover;
}

.cartao {
  background: white;
  margin-left: auto;
  margin-right: auto;
  padding: 15px;
}

.cartao.conteudo {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;
}

h5.nome_produto {

}

.cartao.conteudo.topo {
  margin-top: 0px;
  width: 100%
}

/*
.cartao.conteudo {
  box-shadow: 0 4px 10px -2px #E2E3E3;
  min-height: 190px;
  border-top: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
*/


.cartao.semborda {
  margin-top: 20px;
  border: 0 none;
  background-color: transparent;
  padding: 0;
}

.bg-content {
  display: none;
}



.imagem_empresa {
  width: 80px;
  height: 80px;
  float: left ;
}

.detalhes_empresa  {
  float:left;
  margin-left: 10px;
  display: inline-block;
  width: calc(100% - 90px);
}

.nome_empresa {
  font-size: 24px;
  color: black;
  font-weight: 500;
  display: block;
  line-height: 1.2;
  max-height: 1.2em;
  overflow: hidden;
}

.endereco {
  font-size: 11px;
}

.whatsapp {
  display: block;
  margin-bottom: 5px;
}

.whatsapp span{
  font-size:12px;
  font-weight: 600;

  color: #199D0F
}

.dados_empresa {
  min-height: 90px;
  overflow: hidden;
}

.linha {
  border-bottom: #EFEFEF solid 1px;
}

.descricao_empresa {
  margin: 10px;
  font-size: 12px;
  font-weight: 400;

}

.menu {
  color: #525252;
  margin-top: 15px;

}

.brinde {
  margin-top: 10px;
}
.valor {
  position: absolute;
  color: white;
  font-size: 20px;
  top: 10px;
  width: 100%;
  text-align: center;
}

.row {
  padding-left: 5px;
  padding-right: 5px;
}

.brinde {
  text-align: center;
  position: relative;
}

.preco_troca {
  font-weight: 600;
}

.nome_brinde {
  display: inline-block;
  margin-top: 5px;
  font-size: 16px;
  background: #4b4b4b;
  color: white;
  margin-left: 2px;
  padding: 5px 10px 5px 10px;
  border-radius: 50px;
  font-weight: 200;
}

.foto_brinde {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 30px;
  margin-top: 5px;
  width: 100%;
}


.foto_ambiente {
  display: block;
  float:none;
  margin:0 auto;
  border-radius: 20px;
}

.nome_brinde_pontos {
  font-size: 15px;
  font-weight: 600;

}

.botoes {
  margin: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
}

.botao {
  padding: 15px;
}

.botao.verde{
  background: #6DB31B;
  color:  white;
}

.botao.azul {
  border: #1c95d4 solid 1px;
  margin-top: 10px;
  color: #1c95d4;
}

.icone.whatsapp {
  width: 16px;
  display: inline-block;
  margin: 0
}


.float{
  position:fixed;
  width:60px;
  height:60px;
  bottom:40px;
  right:40px;
  background-color:#25d366;
  color:#FFF;
  border-radius:50px;
  text-align:center;
  font-size:30px;
  box-shadow: 2px 2px 3px #999;
  z-index:100;
}

.my-float{
  margin-top:16px;
}


.fidelidade {
  height: 16px;

  width: 24px;
  background: #3B86FF;
  text-align: center;
  float:left;
  line-height: 1em;
  margin: 2px 3px;
}

.azul .coracao{
  display: inline-block;
  fill: white;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin: 0;
}

.azul {
  color: #3B86FF;
  font-weight: bold;
}


.bolinha {
  margin: 5px;
  width: 8px;
  height: 8px;
  background: #6DB31B;
  border-radius: 100px;
  float: left;
}

.horario {
  padding-top: 2px;
  margin-left: 7px;
  width: 100%;
}

.bolinha.fechado {
  background: red;
}

.horario .descricao {
  font-size: 11px;
  font-weight: bold;
}

.icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 5px;
}

.slides-fotos h3, .slides-produtos h3{
  text-align: center;
  line-height: 32px;
}

.slides-fotos, .slides-produtos {
  position: fixed;
  overflow: auto;
  z-index: 1000;
  top:0px;
  background: white;
  height: 100%;
  width: 100%;
}

.slides-produtos {
  padding-top: 60px;
  background: rgba(255, 255, 255, 0.9);
  height: 100%;
}

.cartao.descricao {
  margin-top: 15px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-right-radius: 10px;
  box-shadow: 0 4px 10px -2px #E2E3E3;

}

.grande {
  font-size: 18px;
  font-weight: bold;
}

.botao_produto {
  border: 1px solid black;
  padding: 15px;
  border-radius: 30px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
}

.botao_produto.verde {
  border: 1px solid #3fad36;
  color: #3fad36;

}

.titulo-produtos {
  background: white;
}
.slides-produtos .icon-fechar {
  width: 32px;
  height: 32px;
  float: left;
  margin-left: 0;
  margin-top: 9px;
}




.chinainbox{
  .nav-tabs{
    border-bottom-color: #e52a2845 !important;
    border-bottom-width: 1px !important;
  }
  .nav-bordered a.active {
    border-bottom: 2px solid #e52a28 !important;
  }
}



@media screen and (min-width: 768px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos   {
    max-width: 90%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .bg-content {
    z-index: 0;
    position: absolute;
    left: 0px;
    top: 0px;
    right: 0px;
    width: 120%;
    height: 120%;
    margin-left: -10%;
    background-position: 0px 0px, 50% 50%;
    background-size: auto, cover;
    background-repeat: repeat, no-repeat;
    opacity: 1;
    -webkit-filter: blur(24px);
    filter: blur(24px);
    display: block;

  }

  .content {
    position: relative;
    z-index: 10;

  }

  .slides-fotos, .slides-produtos {
    left: 20%;
  }

  .sobre_nos {
    border-radius: 5px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
    padding: 10px;
    padding-bottom: 5px;
    background: white;
    margin-top: 10px;
  }
  .brinde {
    margin-top: 0 ;
  }


  .capa_empresa.centralizada {
    height: 310px;
    max-width: 90%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .capa_empresa.desfocada {
    height: 305px;
  }

  .cartao.conteudo {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 30px;
    border-top-right-radius: 0px;
    box-shadow: 0 4px 10px -2px #E2E3E3;
  }

  .cartao.conteudo.topo {
    margin-top: 0px;
  }
}

.icone.insta {
  fill: #525252;
}

.cinza a {
  color: #525252 ;
}

.FlexEmbed {
  display: block;
  overflow: hidden;
  position: relative;
}

.FlexEmbed:before {
  content: "";
  display: block;
  width: 100%;
}


.FlexEmbed--2by1:before {
  padding-bottom: 25%;
}

.FlexEmbed.desfocada {
  background-position: 0px 0px, 50% 50%;
  background-size: auto, cover;
  background-repeat: repeat, no-repeat;
  opacity: 1;
  -webkit-filter: blur(12px);
  filter: blur(12px);
  z-index: -1;
  position: absolute;
  width: 100%;
  max-width: 100%;
  display: block;
  top: 0px;
}

.CoverImage {
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 0 auto;
  max-height: 300px;
  max-width: 90%;
}

.megamenu {
  position: relative;
  border: 1px solid #dcdcdc;
  padding-right: 30px;
  padding-left: 0;

  .wraper {
    position: relative;
    transition: 1s;
  }

  .k-i-arrow-chevron-left, .k-i-arrow-chevron-right {
    position: absolute;
    font-size: 30px;
    top: 7px;
    cursor: pointer;
    color: #999;
    z-index: 10;
    background: #fff;
    transition: 2s;
  }

  .k-i-arrow-chevron-right {
    right: 0;
  }

  .k-i-arrow-chevron-left{
    left: -1px;
    opacity: 0;
    z-index: -1;
    &.exibir{
      opacity: 1;
      z-index: 10;
    }
  }



  ul{
    margin: 0;
    padding: 0;
    background-color: #fff;
    font-family: Arial;
    white-space:nowrap;
    overflow: hidden;
    transition: 1s;
  }

  /* Links inside the navbar */
  a {
    float: left;
    font-size: 14px;
    text-align: center;
    padding: 7px 8px;
    text-decoration: none;
    color: #0b0b0b;
  }

  h4{
    color: #56167D;
    font-size: 14px;
  }


  /* The dropdown container */
  .dropdown {
    display: inline;
    overflow: hidden;
    position: inherit;
    transition: 1s;
    .dropbtn {
      font-size: 15px !important;
      padding: 12px 15px;
      border: none;
      outline: none;
      color: #333;
      background-color: inherit;
      font: inherit; /* Important for vertical align on mobile phones */
      margin: 0; /* Important for vertical align on mobile phones */
      text-transform: uppercase;
    }
  }

  /* Add a red background color to navbar links on hover */
  a:hover, .dropdown:hover .dropbtn {
    position: inherit !important;
  }



  /* Dropdown content (hidden by default) */
  .dropdown-content {
    border: 1px solid #dcdcdc;
    display: none;
    position: absolute;
    background-color: #fff;
    width: 100%;
    left: 0;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1;
  }


  /* Show the dropdown menu on hover */
  .dropdown:hover .dropdown-content {
    display: block;
  }

  /* Create three equal columns that floats next to each other */
  .coluna {
    float: left;
    width: 33.33%;
    padding: 10px;
    background-color: #fff;

    .grupo{
       &.esconder{
         max-height: 220px;
         overflow: hidden;
       }
    }
  }

  /* Style links inside the columns */
  .coluna a {
    float: none;
    background-color: #fff;
    padding: 5px;
    text-decoration: none;
    display: block;
    text-align: left;
  }

  /* Add a background color on hover */
  .coluna a:hover {
    background-color: #ddd;
  }

  /* Clear floats after the columns */
  .linha:after {
    content: "";
    display: table;
    clear: both;
  }
}

.left-side-menu{
  left: 0px;
  bottom: 0px;
  top: auto;
  width: 80%;
  transition: all .2s ease-out;
  z-index: 10010 !important;
  padding-top: 0px;

  .titulo{
    padding-top: 15px !important;
    background: #000000c9;
    padding-bottom: 15px !important;
    h4{
      color: #fff
    }
  }


  &.exibir{
    display: block;
  }

  .slimscroll-menu{
    overflow: scroll;
    max-height: 800px;
  }

   ul > li > a {
    color: #6e768e;
    display: block;
    padding: 10px 15px;
    position: relative;
    transition: all 0.4s;
    font-family: "Poppins", sans-serif;
    font-size: .875rem;
  }
}

.modal-backdrop  {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #333;
  opacity: .5;
  z-index: 10001 !important;
}

@media (max-width: 992px) {

  .megamenu{
    .coluna {
      width: 100%;
      .grupo{
        max-height: inherit;
        height: auto;
      }
    }
  }

  .nome_empresa {
    font-size: 16px !important;
  }

  .cartao.conteudo.topo {
    width: 100%;
  }
  .cartao{
    padding: 10px;
  }

  ::ng-deep.mat-tab-header {
    margin-left: -19px !important;
    margin-right: -19px !important;
  }
}

@media (min-width: 992px) {
  .cartao.conteudo, .cartao.semborda, .slides-fotos, .slides-produtos {
    max-width: 100%;
    /* text-align: center; */
    margin-left: auto;
    margin-right: auto;
  }

  .capa_empresa.centralizada {
    height: 310px;
    max-width: 100%;
    margin: 0 auto;
    box-shadow: 0 4px 10px -2px #a9aaaa;
  }

  .CoverImage {
    max-width: 100%;
  }

  .tirar_margem{
    margin-left: 0 !important;
    margin-right: 0 !important;

  }
}

.header {
  position: sticky;
  top: -1px;
  background: #fff;
  z-index: 999;
  .fa-search{
    color: #333;font-size: 16px;
  }
}

.remova_padding {
  margin-left: -12px;
  margin-right: -12px;
}

.bg-light{
  background-color: #f7f8f8 !important;
}

::ng-deep .tema-personalizado {
  .bg-light, .badge-danger, .badge-success {
    background-color: var(--cor-fundo-elementos) !important;
    color: var(--cor-texto) !important;
  }

  .badge-danger {
    background-color: color-mix(in srgb, var(--cor-fundo) 90%, black) !important;
  }

  h4 {
    color: var(--cor-texto-primaria) !important;

    &.preco {
      color: var(--cor-destaque) !important;
    }
  }

  .headerCampo {
    .text-muted {
      color: color-mix(in srgb, var(--cor-texto) 85%, black) !important;
    }
  }
}
::ng-deep .nav-tabs {
  overflow-x: hidden;
  overflow-y: hidden;
  flex-wrap: nowrap;
  padding-bottom: 2px;
  border-bottom: none;
  position: sticky;
  top: 55px;
  z-index: 9999;
  border-bottom: solid 1px #eaeaea;
  background: #fff;
  width: 100%;
}

::ng-deep .mobile .nav-tabs {
  overflow-x: scroll;
}

::ng-deep .mobile .nav-tabs::-webkit-scrollbar {
  display: none;
}

::ng-deep .nav-tabs .nav-link {
  font-size: 14px;
  color: #a6a5a5;
}

::ng-deep .nav-bordered li a {
  padding: 15px 20px !important;
}

::ng-deep .nav-tabs .nav-link.active, ::ng-deep .nav-tabs .nav-item.show .nav-link {
  background: #fff;
  font-weight: bold;
}

::ng-deep .mobile .nav-tabs {
  border-bottom: solid 1px var(--cor-borda) !important;
  box-shadow: 0 3px 5px #e9e9e9;
}

::ng-deep .nav-tabs li {
  white-space: nowrap;
}

::ng-deep .tab-content {
  padding: 5px;
}

.tirar_margem {
  margin-left: -12px;
  margin-right: -12px;
}

.wrapper-busca{
  padding: 20px;
  padding-left: 0;
  padding-right: 10px;
}

.font-11{
  font-size: 11px  !important;
}
.font-12{
  font-size: 12px !important;
}

.ver_todos {
  display: none;
  a {
    border: 1px solid #7e57c2;
    border-radius: 7px;
    padding-left: 5px;
    padding-right: 5px;
    font-size: 14px;
  }
}

.arrow {
  display: none;
}

.ecommerce {
  .secaoCategoria.moveu {
    .arrow.esquerda {
      display: block;
    }
  }

  &.secaoCategoria.moveu {
    .arrow.esquerda {
      display: block;
    }
  }

  .secaoCategoria.scroll_fim {
    .arrow.direita {
      display: none;
    }
  }

  .pacadenovo{
    .arrow{
      top: 90px;
    }
  }

  .produtos_categoria {
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .scroll_categoria {
    grid-auto-flow: column;
    display: grid;
    width: fit-content;
    grid-template-columns: inherit !important;
  }

  ::ng-deep .produto {
    width: 240px !important;
    min-height: 145px;
    border: solid 1px #efefef;
    border-radius: 5px;
    padding: 5px;
    margin: 5px;
    padding-bottom: 0px !important;
    margin-bottom: 0px !important;

    .container_foto {
      height: 145px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .descricao {
      margin-top: 10px;
    }

    .preco {
      margin-top: 3px;
    }
  }
  .vitrine{
    ::ng-deep .produto {
      min-height: 250px;
    }

    ::ng-deep .container_foto {
      height: 250px;
    }
  }

  .arrow {
    position: absolute;
    display: block;
    padding: 6px 12px;
    border: solid 1px #e2e2e2;
    background: #fff;
    border-radius: 100px;
    font-size: 16px;
    color: #2f2f2f;
    cursor: pointer;
    z-index: 1;
    top: 130px;

    &.direita {
      right: -10px;
    }

    &.esquerda {
      left: -15px;
      display: none;
    }
  }

  .ver_todos {
    position: absolute;
    right: 0px;
    top: 0px;
    display: block;
  }
}



::ng-deep .navImagem .ng-image-slider .image {
  object-fit: contain;
  cursor: pointer;
  right: auto !important;
}

::ng-deep .navImagem .ng-image-slider .ng-image-slider-container .main .main-inner .img-div {
  box-shadow: none;
}

hr {
  border-top: solid 1px #eae9e9;
}

.scroll_categoria {
  display: grid;
  grid-template-columns: 50% 50%;
  gap: 15px 0px;
}

@media only screen and (max-width: 768px) {
  .scroll_categoria {
    display: grid;
    grid-template-columns: 100%;
  }
}

::ng-deep .natal {
  h4 {
    &.categoria {
      color: #730101 !important;
    }
  }

  .tab_categoria {
    color: #d39797 !important;
  }

  .tab_categoria {
    &.active {
      color: #730101 !important;
      border-bottom: 2px solid #730101 !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}


::ng-deep .arraia {
  h4 {
    &.categoria {
      color: #CE2400 !important;
    }
  }

  .tab_categoria {
    color: #5B5764 !important;
  }

  .tab_categoria {
    &.active {
      color: #CE2400 !important;
      border-bottom: 2px solid #CE2400 !important;
    }
  }

  .nome-produto {
    color: #CE2400 !important;
  }
}

::ng-deep .copa_2022 {
  h4 {
    &.categoria {
      color: #1a7200 !important;
    }
  }

  .ver_todos {
    display: none;
    a {
      background: #fff;
      color: #1a7200;
      border: 1px solid #1a7200 !important;
      border-radius: 7px;
      padding-left: 5px;
      padding-right: 5px;
      font-size: 14px;
    }
  }

  .nav-tabs {
    border-bottom: solid 1px #ffd80e !important;
  }

  .tab_categoria {
    color: #9abe90 !important;
  }

  .tab_categoria {
    &.active {
      color: #1a7200 !important;
      border-bottom: 2px solid #ffd80e !important;
    }
  }

  .nome-produto {
    color: #1a7200 !important;
  }
}

::ng-deep .pascoa {
  h4 {
    &.categoria {
      color: #730101 !important;
    }
  }

  .tab_categoria {
    color: #8a6757 !important;
  }

  .tab_categoria {
    &.active {
      color: #FB1E41 !important;
      border-bottom: 2px solid #FB1E41 !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}

::ng-deep .ano_novo {
  h4 {
    &.categoria {
      color: #030303 !important;
    }
  }

  .tab_categoria {
    color: #7f7f7f !important;
  }

  .tab_categoria {
    &.active {
      color: #030303 !important;
      border-bottom: 2px solid #f1dfa5 !important;
    }
  }

  .nav-bordered {
    border-bottom-width: 1px !important;
  }
}

::ng-deep .dia_maes {
  h4 {
    &.categoria {
      color: #FD6C67 !important;
    }
  }

  .tab_categoria {
    color: #8a6757 !important;
  }

  .tab_categoria {
    &.active {
      color: #FD6C67 !important;
      border-bottom: 2px solid #F3ADAC !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}

$corFundo: #0e0e0e;

.carnaval{
  span.h4 , .nav-tabs .nav-link {
    color: #ffffff !important;
  }

  .nav-tabs .nav-link.active {
    color: #fff000 !important;
  }

  h4, a {
    &.categoria {
      color: #fff000 !important;
    }
  }
}

::ng-deep .black_friday_2022 {
  hr {
    border-top: solid 1px #676767;
  }

  .carrinho_desktop {
    background: $corFundo !important;
  }

  .header {
    background: $corFundo;
  }

  span.h4 {
    color: #b57e2b !important;
  }

  h4 {
    &.categoria {
      color: #b57e2b !important;
    }
  }

  .tab_categoria {
    color: #676767 !important;
  }

  .tab_categoria {
    &.active {
      color: #b57e2b !important;
      border-bottom: 2px solid #b57e2b !important;
      background-color: transparent !important;
    }
  }

  .produtos_categoria {
    .produto {
      background: transparent !important;
      border: solid 1px #1a1a1a !important;
    }
  }

  .nav-tabs {
    background: #0e0e0e;
    box-shadow: none;
  }

  .nav-tabs .nav-link.active, ::ng-deep .nav-tabs .nav-item.show .nav-link {
    background: #fff;
    font-weight: bold;
  }

  .nome-produto {
    color: #F6A844 !important;
  }
}

::ng-deep .dia_pais {
  span.h4 {
    color: #10bde9;
  }

  h4 {
    &.categoria {
      color: #10bde9 !important;
    }
  }

  .tab_categoria {
    color: #10bde9 !important;
  }

  .tab_categoria {
    &.active {
      color: #0000dd !important;
      border-bottom: 2px solid #0000dd !important;
    }
  }

  .nome-produto {
    color: #10bde9 !important;
  }
}

::ng-deep .dia_namorados {
  h4 {
    &.categoria {
      color: #b264da !important;
    }
  }

  .tab_categoria {
    color: #b264da !important;
  }

  .tab_categoria {
    &.active {
      color: #d64860 !important;
      border-bottom: 2px solid #F3ADAC !important;
    }
  }

  .nome-produto {
    color: #730101 !important;
  }
}

.categoria_ia {
  padding: 10px 15px;
  background: #ccc;
  border: solid 1px #efefef;
  border-radius: 5px;
  background: #faf7f7;
}

::ng-deep .dialog-produto-ia {
  overflow-x: hidden;
  position: initial;
}

::ng-deep .mobile .dialog-produto-ia {
  padding: 12px;
}

::ng-deep .dialog-produto-ia {
  padding: 0px !important;
}

::ng-deep .cacau_show {
  .produtos_categoria {
    background-color: var(--cor-fundo-site);

    .produto {
      background-color: var(--cor-fundo-elementos);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      border: none;

      .preco {
        color: var(--cor-preco);
        font-weight: bold;
      }

      .nome {
        color: var(--cor-texto-primaria);
        font-weight: 500;
      }

      .descricao {
        color: var(--cor-texto-secundaria);
        font-size: 0.9em;
      }

      &:hover {
        background-color: var(--cor-fundo-elementos);
        border: none;
      }

      img {
        border-radius: 6px;
        object-fit: cover;
      }

      .a-partir-de {
        color: var(--cor-texto-secundaria);
        font-size: 0.9em;
      }

      .badge {
        background-color: var(--cor-destaque);
        color: var(--cor-texto-botao);
        border-radius: 4px;
        padding: 3px 6px;
        font-size: 0.8em;
        font-weight: normal;
      }
    }
  }

  /* Estilos para as abas de navegação */
  #tabs {
    background-color: var(--cor-fundo-elementos) !important;
    border-bottom: 1px solid var(--cor-borda) !important;
    box-shadow: none !important;
  }

  .nav-tabs {
    background-color: var(--cor-fundo-elementos) !important;
    border-bottom: 1px solid var(--cor-borda) !important;
    box-shadow: none !important;
  }

  .nav-item {
    margin-bottom: 0 !important;
  }

  .nav-link, .tab_categoria {
    color: var(--cor-texto-secundaria) !important;
    background-color: transparent !important;
    border: none !important;
    transition: all 0.3s ease;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 3px;
      background-color: var(--cor-destaque);
      transition: width 0.3s ease;
    }

    &:hover {
      color: var(--cor-texto-primaria) !important;

      &::after {
        width: 100%;
      }
    }

    &.active {
      color: var(--cor-destaque) !important;
      font-weight: 600 !important;
      background-color: transparent !important;
      border: none !important;

      &::after {
        width: 100%;
      }
    }
  }

  /* Estilos específicos para as abas de navegação */
  .nav-tabs .nav-item .tab_categoria {
    color: var(--cor-texto-secundaria) !important;
    background-color: transparent !important;
    border: none !important;
    transition: all 0.3s ease;
    position: relative;
    padding: 15px 20px;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 0;
      height: 3px;
      background-color: var(--cor-destaque);
      transition: width 0.3s ease;
    }

    &:hover {
      color: var(--cor-texto-primaria) !important;

      &::after {
        width: 100%;
      }
    }

    &.active {
      color: var(--cor-destaque) !important;
      font-weight: 600 !important;
      background-color: transparent !important;
      border: none !important;

      &::after {
        width: 100%;
      }
    }
  }
}


.produtos_categoria {
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
  }

}
