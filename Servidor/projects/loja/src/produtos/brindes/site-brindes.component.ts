import {Component, EventEmitter, HostListener, Input, OnInit, Output, ViewEncapsulation} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {DominiosService} from "../../services/dominios.service";
import {ITela} from "../../objeto/ITela";
import {ProdutoService} from "../../services/produto.service";
import {ConstantsService} from "../../services/ConstantsService";
import {SiteProdutoComponent} from "../../site-produto/site-produto.component";
import {MyDetectorDevice} from "../../app/shared/MyDetectorDevice";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";
import {AutorizacaoLojaService} from "../../services/autorizacao-loja.service";
import {AgrupadorBrindesUtils} from "../../objeto/AgrupadorBrindesUtils";
import {ClienteService} from "../../services/cliente.service";
import {CarrinhoService} from "../../services/carrinho.service";
import {PainelLoginComponent} from "../../app/painel-login/painel-login.component";
import {ValidarContatoZapComponent} from "../../app/cad-contato/validar-contato-zap/validar-contato-zap.component";

@Component({
  selector: 'app-site-brindes',
  templateUrl: './site-brindes.component.html',
  styleUrls: ['./site-brindes.component.scss']
})
export class SiteBrindesComponent   implements ITela, OnInit {
  pedido: any;
  tema: string
  brindes: any = [];
  carregando = true;
  window: DialogRef;
  isMobile: any;
  usuario: any = {};
  faixas: any  = [ ];
  brindesPorFaixa: any  =  { '0': []};
  saldoDisponivel = 0;
  saldoCartao = 0;
  fazerLogin: boolean;
  acumulo: string;
  constructor(private router: Router, private dominiosService: DominiosService,  private carrinhoService: CarrinhoService,
              private produtoService: ProdutoService, private autorizacao: AutorizacaoLojaService ,
              private detectorDevice: MyDetectorDevice,   private clienteService: ClienteService,
              private dialogService: DialogService, private location: Location,
              private activatedRoute: ActivatedRoute, private constantsService: ConstantsService) {
    this.isMobile = this.detectorDevice.isMobile();

  }

  ngOnInit(): void {
    this.tema =  window['tema'];

    this.constantsService.empresa$.subscribe(async (empresa) => {
      if (!empresa) return

      if(empresa.integracaoPedidoFidelidade && empresa.integracaoPedidoFidelidade.plano)
        this.acumulo =   empresa.integracaoPedidoFidelidade.plano.tipoDeAcumulo === 'Selos' ? 'selos' : 'pts'

      await this.carregueSaldoUsuario();

      this.produtoService.listeAhVenda(empresa).subscribe( (respota: any) => {
        this.brindes = respota.brindes || [];

        this.brindes.sort((a, b) => a.valorResgate - b.valorResgate);

        this.faixas = AgrupadorBrindesUtils.agrupeBrindes(this.brindes)

        for(let i = 0; i < this.faixas.length; i ++){
          let faixa: number = this.faixas[i]

          let brindes: any = this.brindes.filter((item: any) => (item.valorResgate <= faixa && !item.faixa) || item.faixa === faixa);

          //naodisponivel
          brindes.forEach((item: any) => {
            if(! item.faixa)
              item.faixa = faixa;

          })

          this.brindesPorFaixa[faixa] =   brindes
        }
        this.setSaldoDisponivel();
        this.carregouBrindes();
      });
    });
  }

  async carregueSaldoUsuario(){
    return new Promise((resolve) => {
      this.clienteService.obtenhaSaldoResgate().then( (dados: any) => {
        this.saldoCartao = dados.saldo;
        this.fazerLogin = dados.fazerLogin;
        resolve(null);
      });
    })

  }

  abraDetalhesProduto(produto: any, indiceProduto: any = null) {
    if(this.saldoDisponivel  >= produto.valorResgate)  {
      this.window = SiteProdutoComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService, this.isMobile, produto, indiceProduto, null, () => {
            this.setSaldoDisponivel();
        });
    }

  }

  private carregouBrindes() {
    this.carregando = false;
  }

  irParaLogin(){
    if(this.isMobile){
      const telafone: string  =   this.pedido && this.pedido.contato ?  this.pedido.contato.telefone : null;
      this.dominiosService.vaParaLogin(window.location.pathname, telafone)
    } else {
      PainelLoginComponent.abraComoPopup(this.router, this.location, this.activatedRoute,  this.dialogService, null, async () => {
        await this.carregueSaldoUsuario();
        this.setSaldoDisponivel();
      } );
    }

    return false;
  }

  irParaVerSaldo(){
    if(this.isMobile){
      this.dominiosService.vaParaValidarLogin(window.location.pathname, this.pedido.contato)
    } else {
      ValidarContatoZapComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
        this.dialogService,  this.pedido.contato , async (result) => {
          if(result && result.login) {
            await this.carregueSaldoUsuario();
            this.setSaldoDisponivel();
          }
        });
    }

    return false;
  }

  private setSaldoDisponivel() {
    this.saldoDisponivel =  this.saldoCartao;
    this.pedido = this.carrinhoService.obtenhaPedido();

    if(this.pedido && this.pedido.totalResgatado)
      this.saldoDisponivel -= this.pedido.totalResgatado;

    if(this.saldoDisponivel < 0)
      this.saldoDisponivel = 0


    //naodisponivel
    this.brindes.forEach((item: any) => {
      item.naodisponivel =    item.valorResgate > this.saldoDisponivel;
      item.acumulo = this.acumulo;
    })
  }



  deveExibirTopo() {
    return true;
  }

  deveExibirMenu(){
    return true;
  }


  deveExibirBannerTema() {
    return true;
  }

  deveTerBordas() {
    return true;
  }


}
