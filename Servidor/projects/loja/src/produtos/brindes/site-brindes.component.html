<div *ngIf="saldoDisponivel >=0 && !carregando"  class="boxinfo mt-2 font-weight-bold" [hidden]="fazerLogin"  >
  <p class="mb-0 ">Você tem disponível </p>
  <span class="font-19 saldo"><b> {{saldoDisponivel}}</b> {{acumulo}}</span>
</div>

<div *ngIf="fazerLogin" class="mt-2 boxinfo">
  <a  (click)="irParaLogin()"  href="">
    <b>Identifique-se</b>

  </a>
  <br>
  <p  class="font-12">Faça login para utilizar seus pontos e resgatar brindes</p>
  <button (click)="irParaVerSaldo()" class="btn btn-blue btn-xs mt-1">Ver meu saldo</button>
  <br>
  <!--<button (click)="irParaVerSaldo()" class="btn btn-blue btn-xs mt-1">Ver meu saldo</button> -->
</div>

<div class="  mt-3" *ngFor="let faixa of faixas">

  <h2 class="mb-4">Recompensas de até {{faixa}} {{acumulo}}</h2>

  <div class="" style="        overflow-x: auto;">
    <div class="scroll_categoria">
      <div class="brinde"  *ngFor="let brinde of brindesPorFaixa[faixa]" [ngClass]="{'naodisponivel': brinde.naodisponivel}">
        <div class="reward-card mb-3" (click)="abraDetalhesProduto(brinde)" >
         <span class="points-badge">
                        <svg class="points-icon" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" fill="#FFF"/>
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z"
                                  />
                        </svg>
           {{brinde.valorResgate}}  {{brinde.acumulo}}
          </span>
          <div class="product-icon">
            <svg width="80" height="80" viewBox="0 0 100 100"  *ngIf="!brinde.imagens || !brinde.imagens[0]">
              <path fill="#CD8032" d="M20,40 L80,40 L75,90 L25,90 Z"/>
              <path fill="#8B4513" d="M30,30 L70,30 C75,30 75,40 70,40 L30,40 C25,40 25,30 30,30"/>
            </svg>

            <img  class="img img-fluid mb-1" *ngIf="brinde.imagens?.length"
                  [src]="'/images/empresa/' + brinde.imagens[0].linkImagem" alt="Imagem">

          </div>

          <div class="p-2 branco">
            <h5 class="mb-0">{{brinde.nome}}</h5>
          </div>
        </div>
      </div>
    </div>
  </div>


</div>

<div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando" style="font-size: 40px;height: 90px;" ></div>


<div class="   boxinfo mt-2 text-center" *ngIf="!faixas.length && !carregando">
  <span class="fe-award mb-2 d-block fa-2x  text-center"></span>
  <p>Nenhum brinde disponível para resgate ainda!</p>
</div>


<div style=" height: 50px">

</div>
