<div class="ml-2"  *ngIf="pagamento.formaDePagamento?.nome === 'dinheiro'">
    <div  class="mt-2" >
      <h5 class="d-inline">Vai precisar de troco?  </h5>
      <div class="form-group mb-2  ml-3 mt-2 radio radio-blue">

        <input id="trocoSim" name="temTroco" type="radio"    kendoRadioButton  class="k-radio"
               [(ngModel)]="pagamento.temTroco" value="sim"
               [required]="true"/>
        <label for="trocoSim" class="mr-4">&nbsp;Sim</label>

        <input id="trocoNao" name="temTroco" type="radio"    kendoRadioButton  class="k-radio"
               [(ngModel)]="pagamento.temTroco" value="nao" (ngModelChange)="precisaDeTroco(false)"
               [required]="true"/>
        <label for="trocoNao" class="ml-1">&nbsp;Não</label>

        <div class="invalid-feedback" *ngIf="!pagamento.temTroco">
          Informe se vai precisar de troco.
        </div>
      </div>
    </div>

    <div id="divTroco"  *ngIf="pagamento.temTroco ==='sim'">
      <div class="form-group ">
        <h5 for="trocoParaM">Troco para quanto? </h5>
        <input id="trocoParaM" name="trocoParaM" type="text" inputmode="decimal" class="form-control troco"
               #txtTrocoM="ngModel"  [trocoMinimo]="obtenhaTotalPagar()"
               (ngModelChange)="calculeTroco($event)" style="width: 160px"
               [(ngModel)]="pagamento.trocoPara" appSelecionarNoFoco
               currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',', align: 'left' }"/>
        <div class="invalid-feedback">
          <p *ngIf="txtTrocoM.errors?.trocoMinimo">
            A nota tem que ser maior que  {{obtenhaTotalPagar() | currency: "R$"}}</p>
        </div>
      </div>
    </div>

    <div class="produto pt-0 pb-2" *ngIf="pedido.troco !== null">

      <div class="media mt-0" >
        <div class="media-body">
          <h5 class="mt-0 mb-1"><span>dinheiro</span></h5>
        </div>
        <h5 class="mt-0">{{pagamento.trocoPara | currency: 'BRL'}}</h5>
      </div>
      <div class="media mt-1" *ngIf="pagamento.trocoPara > 0">
        <div class="media-body">
          <h4 class="mt-0 mb-1"><span>Troco</span></h4>
        </div>
        <h4 class="mt-0 preco" [class.negativo]="pedido.troco < 0">{{pedido.troco | currency: 'BRL'}}</h4>
      </div>

      <hr class="linha">
    </div>

  </div>
