<app-header-tela [titulo]="'Monte sua pizza'" [exibirFechar]="true" (fechou)="fecheTela()" ></app-header-tela>

<div class="k-i-loading ml-1 mr-1 centralizado" *ngIf="carregando"   ></div>

<h4>{{produtoCategoria.nome}}</h4>

<p>{{produtoCategoria.descricao}}</p>

<h4 class="preco ">
  <span   class="text-black-50 font-14  ">A partir de</span>
  {{produtoCategoria.valorMinimo | currency: 'BRL'}}

</h4>

<app-adicionais-customizados #adicionaisCustomizados [itemPedido]="itemPedido"   [montarPizza]="true"
                             (alterouTamanho)="onAlterouTamanho($event)"   (saboresSelecionados)="onEscoheuSabor($event)" >

</app-adicionais-customizados>


<div *ngFor="let campoAdicional of itemPedido.produto.camposAdicionais; let posicao = index">

  <app-site-campo-adicional #adicionalComponent [id]="'adicional_' + campoAdicional.id" [campoAdicional]="campoAdicional"
                            [itemPedido]="itemPedido" [posicao]="campoAdicional.posicao"></app-site-campo-adicional>
</div>


<div class="mt-2">
  <div class="mt-3"></div>
  <div class="form-group mb-3">
    <label for="observacao"><i class="fas fa-comment-dots"></i> Alguma Observação?</label>
    <textarea type="text" class="form-control" autocomplete="off" maxlength="255"
              id="observacao" name="observacao" [(ngModel)]="itemPedido.observacao" #observacao="ngModel"
              placeholder="Inclua uma observação sobre o pedido." value="" required>
        </textarea>

    <div class="invalid-feedback">
    </div>
  </div>
</div>


<app-site-adicionar-produto #siteAdicionarProduto
    [itemPedido]="itemPedido" [pedido]="pedido" [indiceItem]="indiceItem" [window]="window"  >

</app-site-adicionar-produto>

<div id="alertaFechado" class="modal fade" tabindex="-1" role="dialog"   aria-modal="true">
  <div class="modal-dialog" style="border: solid 1px #f9f9f9;border-radius: 5px;">
    <div class="modal-content">
      <div class="modal-body p-4">
        <div class="text-center">
          <i class="dripicons-information h1 text-info"></i>
          <h4 class="mt-2"  >{{mensagemAbrirPedidos}}</h4>
          <p class="mt-3">Continue olhando nosso cardápio à vontade.</p>
          <button type="button" class="btn btn-info my-2" data-dismiss="modal" >Continuar</button>

          <button class="btn btn-primary ml-2" *ngIf="modoVisualizacao">
            <app-exibir-whatsapp [empresa]="empresa" [light]="true"></app-exibir-whatsapp>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
