import {EventEmitter, Injectable} from '@angular/core';
import {Pedido<PERSON>oja} from "../objeto/PedidoLoja";
import {Pagamento} from "../objeto/Pagamento";
import {Entrega} from "../objeto/Entrega";
import {Endereco} from "../objeto/Endereco";
import {AutorizacaoLojaService} from "./autorizacao-loja.service";
import {EnderecoService} from "./endereco.service";
import {CupomPedido} from "../objeto/CupomPedido";
import {ClienteService} from "./cliente.service";
import {DescontoPromocional} from "../objeto/DescontoPromocional";
import {EnumOrigemPedido} from "../objeto/EnumOrigemPedido";
import {ServerService} from "./ServerService";
import {HttpClient} from "@angular/common/http";

declare var gtag: any;

@Injectable({
  providedIn: 'root'
})
export class CarrinhoService extends ServerService {
  private pedido: PedidoLoja;
  private _key = '_pedido_';
  private _keyMultipedido = '_multipedido_'
  private keyultimo = '_ultimopedido_';

  alterouPedido: EventEmitter<PedidoLoja> = new EventEmitter();
  contexto: string;

  constructor(private autorizacao: AutorizacaoLojaService, private clienteService: ClienteService,
              private enderecoService: EnderecoService, protected http: HttpClient) {
    super(http);
  }

  setContexto(multiloja = false){
    this.contexto = multiloja ? EnumOrigemPedido.Multiloja : EnumOrigemPedido.Loja;
  }

  getKey(){
     if(this.contexto === EnumOrigemPedido.Multiloja)
       return  this._keyMultipedido;

     return this._key;
  }

  private obtenhaPedidoLocal(usuario: any, objPedido: any ){
    if(!objPedido) return null;

    let pedido = new PedidoLoja( this.contexto, usuario);
    pedido.timestamp = objPedido.timestamp;

    pedido.codigo = objPedido.codigo;
    pedido.guid = objPedido.guid;
    pedido.mesa = objPedido.mesa;
    pedido.linkPagamento = objPedido.linkPagamento;
    pedido.gerarLinkPagamento = objPedido.gerarLinkPagamento;
    pedido.pago = objPedido.pago;
    pedido.desconto = objPedido.desconto;
    pedido.codigoCupom = objPedido.codigoCupom;
    pedido.pontosReceber = objPedido.pontosReceber;
    pedido.disparouEventoIniciarCheckout = objPedido.disparouEventoIniciarCheckout;
    pedido.disparouEventoAddedToCart = objPedido.disparouEventoAddedToCart;

    if( objPedido.entrega ) {
      pedido.entrega = Object.assign(new Entrega(), objPedido.entrega);
      if( objPedido.entrega.endereco ) {
        const objEndereco = objPedido.entrega.endereco;

        const latitudeLongitude = objEndereco.localizacao;

        pedido.entrega.endereco = new Endereco(objEndereco.id, objEndereco.cidade,
          objEndereco.cep, objEndereco.logradouro, objEndereco.complemento, objEndereco.bairro,
          objEndereco.numero, objEndereco.descricao, latitudeLongitude);

        pedido.entrega.endereco.taxaDeEntrega = objEndereco.taxaDeEntrega
        pedido.entrega.endereco.zonaDeEntrega = objEndereco.zonaDeEntrega

      }
    }

    if(!pedido.contato || !pedido.contato.id)
      pedido.setContato(objPedido.contato)

    for( const item of objPedido.itens ) {
      pedido.adicione(item.produto, item.qtde, item.observacao, item.adicionais, item.produtoTamanho, item.sabores);
    }

    if(objPedido.cupom)
      pedido.cupom = Object.assign(new CupomPedido(), objPedido.cupom)

    if(objPedido.promocoesAplicadas)
      for(let promocaoAplicada of objPedido.promocoesAplicadas)
        pedido.promocoesAplicadas.push(
          new DescontoPromocional(promocaoAplicada.id, promocaoAplicada.empresa, promocaoAplicada.descricao, promocaoAplicada.desconto))

    if(objPedido.cashback && objPedido.cashback.usar) {
      pedido.cashback = objPedido.cashback
      pedido.calculeTotal();
      pedido.calculeTroco();
    } else{
      delete pedido.cashback
      if(objPedido.resgate  && objPedido.resgate.valor){
        pedido.resgate = objPedido.resgate
        pedido.calculeTotal();
        pedido.calculeTroco();
      }
    }

    if( objPedido.pagamento ) { //versão antiga usava string
      pedido.pagamento = new Pagamento();
      pedido.pagamento.formaDePagamento = objPedido.pagamento.formaDePagamento;
      pedido.pagamento.trocoPara = objPedido.pagamento.trocoPara;
      pedido.pagamento.dadosCartao = objPedido.pagamento.dadosCartao;
      pedido.pagamento.dadosPix = objPedido.pagamento.dadosPix;
      pedido.pagamento.temTroco = objPedido.pagamento.temTroco;
      pedido.pagamento.tipoDePagamento = objPedido.pagamento.tipoDePagamento;

      pedido.calculeTotal();
      pedido.calculeTroco();
    }

    if(objPedido.dataEntrega) {
      pedido.dataEntrega = new Date(objPedido.dataEntrega)
      pedido.horarioEntrega = objPedido.horarioEntrega

    }

    return pedido;

  }

  private obtenhaPedidoServer(usuario: any, pedidoServer: any, taxaDeEntrega: number){
    let pedido = new PedidoLoja(pedidoServer.origem, usuario);

    pedido.timestamp = new Date().getTime();
    pedido.mesa = pedidoServer.mesa;
    pedido.pago = false;
    pedido.codigoCupom = pedidoServer.codigoCupom;
    pedido.entrega = new Entrega();
    pedido.entrega.formaDeEntrega = pedidoServer.formaDeEntrega;

    if(pedidoServer.endereco){
      pedido.entrega.taxaDeEntrega = taxaDeEntrega;

      const objEndereco = pedidoServer.endereco, latitudeLongitude = objEndereco.localizacao;

      pedido.entrega.endereco = new Endereco(objEndereco.id, objEndereco.cidade,
        objEndereco.cep, objEndereco.logradouro, objEndereco.complemento, objEndereco.bairro,
        objEndereco.numero, objEndereco.descricao, latitudeLongitude);


    }

    if(pedidoServer.dataEntrega) {
      pedido.dataEntrega = new Date(pedidoServer.dataEntrega)
      pedido.horarioEntrega = pedidoServer.horarioEntrega
    }

    for( const item of pedidoServer.itens ){
      if(item.sabores && item.sabores.length){
        item.sabores.forEach( (sabor: any) => {
          // veio do server estrutura diferente
          if( sabor.produto &&  sabor.produto.id){
            sabor.nome = sabor.produto.nome
            sabor.produto = sabor.produto.id
            sabor.preco = sabor.produtoTamanho ? sabor.produtoTamanho.preco : sabor.valor
          }

        })
      }
      pedido.adicione(item.produto, item.qtde, item.observacao, item.adicionais, item.produtoTamanho, item.sabores);
    }

    return pedido;

  }

  private crieNovoPedido(usuario: any, mesa: any){
    this.pedido = new PedidoLoja(this.contexto, usuario, mesa);
    this.pedido.origem = this.contexto;

    this.clienteService.obtenhaGuidPedido().then( (guid) => {
      this.pedido.guid = guid;
      this.salvePedido(this.pedido);
    })

  }

  async refazerPedido(pedidoServer: any, retornoTaxa: any = null){
    let usuario = this.autorizacao.getUsuario();

    let guid: string =  await this.clienteService.obtenhaGuidPedido();

    if(guid){
      let pedido =  this.obtenhaPedidoServer(usuario, pedidoServer  , retornoTaxa ? retornoTaxa.taxaDeEntrega : null);

      pedido.guid = guid;

      if(retornoTaxa )
        pedido.entrega.setTaxaEntrega(pedido.entrega.endereco, retornoTaxa)

      this.pedido = pedido;
      this.salvePedido(this.pedido);
    }

  }

  otenhaPedidoPrato(itemPedidoServer: any){
    let pedido =  this.obtenhaPedidoServer({}, {itens: [itemPedidoServer]}, null );

    return pedido.itens[0];
  }

  obtenhaPedido(mesa: any = null): PedidoLoja {
    let usuario = this.autorizacao.getUsuario();

    if( this.pedido == null ) {
      const jsonPedido = localStorage.getItem(this.getKey());

      if(jsonPedido){
        let pedido =  this.obtenhaPedidoLocal(usuario, JSON.parse(jsonPedido) )

        if(!pedido.expirou()){
          this.pedido = pedido;
        } else { // NOVO EXPIRADO
          this.crieNovoPedido(usuario, mesa)
        }

      } else { // NOVO PEDIDO
         this.crieNovoPedido(usuario, mesa)
      }
    } else {
      if(usuario && usuario.telefone)
        this.pedido.contato = usuario;

      if(mesa){
        if(mesa.id || mesa.expirada){
          this.pedido.mesa = mesa;
          this.salvePedido(this.pedido);
        }
      }
    }

    return this.pedido;
  }

  atualizeTaxaDeEntrega(pedido: any) {
    return new Promise((resolve) => {
      if(!pedido.entrega.ehDelivery() || !pedido.entrega.endereco){
        pedido.calculeTotal();
        return resolve(null);
      }

      this.enderecoService.calculeTaxaDeEntrega(pedido.entrega.formaDeEntrega, pedido.entrega.endereco,
        pedido.obtenhaSubTotal()).then((dados: any) => {
        pedido.entrega.setTaxaEntrega( pedido.entrega.endereco, dados)
        pedido.calculeTotal();
        resolve(null);
      }).catch((erro) => {
        resolve(erro)
      })
    })

  }

  salvePedido(pedido: PedidoLoja) {
    this.pedido.timestamp = new Date().getTime();
    this.alterouPedido.emit(pedido);
    localStorage.setItem(this.getKey(), JSON.stringify(pedido));
  }


  temPedidoNoCarrinho(){
    const jsonPedido = localStorage.getItem(this.getKey());

    if(!jsonPedido) return false;

    const objPedido = JSON.parse(jsonPedido);

    return objPedido.qtde > 0;

  }

  limpePedido(ultimoPedido: any = null) {
    this.pedido = null;
    localStorage.removeItem(this.getKey());

    if(ultimoPedido && ultimoPedido.mesa){// Garantir proximo pedido seja da mesa
      this.pedido  = this.obtenhaPedido(ultimoPedido.mesa)
      this.pedido.contato = ultimoPedido.contato;
    }

    this.alterouPedido.emit(this.pedido);
  }


  limpeContatoPedido() {

    if(this.pedido && this.pedido.contato){
      this.pedido.contato = {};
      this.salvePedido(this.pedido)
    }
  }

  salveUltimoPedido(state: any = {}) {
    state.pedido = this.pedido;
    localStorage.setItem(this.keyultimo, JSON.stringify(state));
    this.limpePedido(state.pedido);
  }

  obtenhaDadosUltimoPedido(){
    let usuario = this.autorizacao.getUsuario();

    const jsondados: any = localStorage.getItem(this.keyultimo);

    if(jsondados ){
      let dados: any = JSON.parse(jsondados);

      dados.pedido = this.obtenhaPedidoLocal(usuario, dados.pedido);

      return dados
    }

    return null;

  }

  obtenhaMesa(hashMesa: any) {
    return new Promise((resolve) => {
      this.clienteService.obtenhaMesaPeloHash(hashMesa).then((mesa: any) => {
        if(!mesa) {
          this.limpePedido()
          this.obtenhaPedido()

          return resolve(null)
        }
        if(this.pedido && (!this.pedido.mesa || this.pedido.mesa.id !== Number(mesa.id))) {
          this.pedido = this.obtenhaPedido(mesa);
          this.salvePedido(this.pedido)
        }
        resolve(mesa)
      }).catch((reason) => {
        this.limpePedido()
        this.obtenhaPedido()
        resolve(null)
      })
    })
  }

  obtenhaMesaPorNome(nome: any) {
    return new Promise((resolve) => {
      this.clienteService.obtenhaMesaPeloHash(nome).then((mesa: any) => {

      });
    });
  }

  atualizeMesa(codigoMesa: any) {
    if(!this.pedido.mesa || this.pedido.mesa.id  !== Number(codigoMesa)){
      this.limpePedido();
      this.clienteService.obtenhaMesa(codigoMesa).then( (mesa: any) => {
        this.pedido = this.obtenhaPedido(mesa);
        this.salvePedido(this.pedido);
      })
    }
  }

  soliciteGarcom() {
    return new Promise((resolve) => {
      if(!this.pedido.mesa) return;

      this.clienteService.soliciteGarcom(this.pedido.mesa.id).then((resultado) => {
        return resolve(resultado)
      })

    })
  }

  soliciteFecharMesa() {
    return new Promise((resolve) => {
      if(!this.pedido.mesa) return;

      this.clienteService.soliciteFecharMesa(this.pedido.mesa.id).then((resultado) => {
        return resolve(resultado)
      })

    })
  }

  atualizeValorDesconto(codigopromo: string, empresa: any, aplicarAuto: any = false) {
    return new Promise((resolve) => {
      let cupomAplicado = this.pedido.cupom;

      if(!codigopromo && this.pedido.cupom) codigopromo = this.pedido.cupom.codigo;

      delete this.pedido.cupom
      this.pedido.desconto = 0;
      this.pedido.codigoCupom = codigopromo;
      this.pedido.calculeTotal();
      let dadosEnvio = this.pedido.obtenhaDadosEnvio(empresa)

      this.clienteService.calculeDescontoPromocoes(dadosEnvio, empresa).then((respostaDescontos) => {
        if(respostaDescontos && respostaDescontos.promocoesAplicadas && respostaDescontos.promocoesAplicadas.length > 0) {
          this.pedido.apliqueDescontoPromocional(respostaDescontos)

          for(let promocaoAplicada of this.pedido.promocoesAplicadas)
            if(!promocaoAplicada.cumulativa) {
              this.salvePedido(this.pedido);
              return resolve(null);
            }

          if(!codigopromo) this.salvePedido(this.pedido);
        } else
          this.pedido.promocoesAplicadas = []

        if(!codigopromo) return  resolve(null);

        this.clienteService.calculeDescontoCupom(codigopromo, this.pedido.obtenhaDadosEnvio(empresa)).then( (cupom) => {

          if (cupom && cupom.id) {
            if( cupomAplicado ) {
              cupom.aplicarAuto = cupomAplicado.aplicarAuto;
            }
            this.pedido.apliqueCupom(cupom);
          }else{
            if(aplicarAuto || (cupomAplicado.aplicarAuto)){
              let cupomInvalido: any = { id: -1 , codigo: codigopromo, aplicarAuto: true, desconto: 0 ,
                brindeResgate: (cupomAplicado && cupomAplicado.brindeResgate)};
              cupomInvalido.erro = cupom.erro;
              this.pedido.apliqueCupom(cupomInvalido);
            }
          }

          this.salvePedido(this.pedido);

          resolve(null);
        })
      })
    })
  }


  atualizeDescontosETaxas(pedido: PedidoLoja, empresa: any): Promise<any> {
    let codigopromo = pedido.cupom ? pedido.cupom.codigo : null;

    return new Promise((resolve) => {
      this.atualizeValorDesconto(codigopromo, empresa).then(() => {
        this.atualizeTaxaDeEntrega(pedido).then( (err) => {
          this.pedido = pedido;
          this.pedido.removaPagamentoSelecionado();
          this.salvePedido(this.pedido);
          resolve(err);
        });
      })
    })
  }

  notifiqueAdicionouCarrinho(itemPedido: any) {
    super.facaPost('/carrinho/adicione-item', itemPedido);
  }

  notifiqueInicioCheckout(pedido: any, idEvento: string) {
    if( typeof gtag !== 'undefined' ) {
      const items = this.pedido.itens.map(item => {
        return {
          id: item.produto.id,
          name: item.produto.nome,
          category: item.produto.categoria ? item.produto.categoria.nome : 'Sem Categoria',
          quantity: item.qtde,
          price: item.total / item.qtde
        }
      });

      gtag('event', 'begin_checkout', {
        currency: 'BRL',
        value: this.pedido.total,
        items: items
      });
    }

    super.facaPost('/carrinho/inicie-checkout', {
      pedido: pedido,
      idEvento: idEvento
    });
  }

  envieEventoCompra(dados: any) {
    return this.facaPost('/carrinho/envie-evento-compra', dados);
  }

  /**
   * Adiciona um item ao pedido e dispara os eventos necessários
   * @param pedido O pedido atual
   * @param itemPedido O item a ser adicionado
   * @param indiceItem Índice do item se for edição, null se for novo item
   */
  adicioneItem(pedido: PedidoLoja, itemPedido: any, indiceItem: any = null) {
    let itemPedidoAdicionado = null;

    if (typeof gtag !== 'undefined') {
      try {
        gtag('event', 'add_to_cart', {
          currency: 'BRL',
          value: itemPedido.total,
          items: [
            {
              id: itemPedido.produto.id,
              name: itemPedido.produto.nome,
              price: itemPedido.total / itemPedido.qtde,
              quantity: itemPedido.qtde
            }
          ]
        });
      } catch (error) {
        console.log(error);
      }
    }

    if (!indiceItem) {
      itemPedidoAdicionado = pedido.adicione(
        itemPedido.produto,
        itemPedido.qtde,
        itemPedido.observacao,
        itemPedido.adicionais,
        itemPedido.produtoTamanho,
        itemPedido.sabores
      );
    } else {
      itemPedidoAdicionado = pedido.edite(
        indiceItem,
        itemPedido.produto,
        itemPedido.qtde,
        itemPedido.observacao,
        itemPedido.adicionais,
        itemPedido.produtoTamanho,
        itemPedido.sabores
      );
    }

    // Atualiza os descontos e taxas do pedido com base na empresa atual
    return this.atualizeDescontosETaxas(pedido, itemPedido.produto.empresa);
  }
}
