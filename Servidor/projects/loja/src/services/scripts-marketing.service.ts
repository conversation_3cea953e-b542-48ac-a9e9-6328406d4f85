import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from './ServerService';

@Injectable({
  providedIn: 'root'
})
export class ScriptsMarketingService extends ServerService {
  constructor(protected http: HttpClient) {
    super(http);
  }

  /**
   * Obtém os scripts de marketing da empresa atual
   */
  obtenhaScriptsMarketing(): Promise<any> {
    return this.obtenha('/empresas/me/scripts', {});
  }

  /**
   * Salva os scripts de marketing da empresa
   * @param scripts Objeto contendo os scripts de marketing (pixelFacebook, analytics, gtm, gtag)
   */
  salveScriptsMarketing(scripts: any): Promise<any> {
    return this.http.put('/empresas/scripts/atualizar', scripts)
      .toPromise()
      .then(this.retorno)
      .catch(this.handleError);
  }
}
