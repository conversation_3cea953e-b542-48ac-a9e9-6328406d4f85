import { Injectable } from '@angular/core';
import { AutorizacaoLojaService } from './autorizacao-loja.service';
import {CarrinhoService} from "./carrinho.service";

declare var fbq: any;
declare var gtag: any;

@Injectable({
  providedIn: 'root'
})
export class PixelTrackingService {
  // Chave de armazenamento local para eventos já enviados
  private readonly PURCHASE_EVENTS_STORAGE_KEY = 'fbp_purchase_events_sent';

  // Chave de sessão para eventos enviados na mesma sessão (evita duplicatas em caso de problemas com localStorage)
  private readonly eventsSentInSession: Set<string> = new Set();

  // Tempo máximo para considerar um evento como válido (24 horas em milissegundos)
  private readonly EVENT_EXPIRATION_TIME = 24 * 60 * 60 * 1000;

  constructor(private autorizacaoLojaService: AutorizacaoLojaService, private carrinhoService: CarrinhoService) {
    // Limpa eventos antigos ao inicializar o serviço
    this.cleanupExpiredEvents();
  }

  /**
   * Obtém os eventos de compra já enviados do localStorage
   */
  private getPurchaseEventsSent(): {[key: string]: number} {
    try {
      const storedEvents = localStorage.getItem(this.PURCHASE_EVENTS_STORAGE_KEY);
      return storedEvents ? JSON.parse(storedEvents) : {};
    } catch (e) {
      console.error('Erro ao obter eventos do localStorage:', e);
      return {};
    }
  }

  /**
   * Salva os eventos já enviados no localStorage
   */
  private savePurchaseEventsSent(events: {[key: string]: number}): void {
    try {
      localStorage.setItem(this.PURCHASE_EVENTS_STORAGE_KEY, JSON.stringify(events));
    } catch (e) {
      console.error('Erro ao salvar eventos no localStorage:', e);
    }
  }

  /**
   * Limpa eventos expirados do armazenamento
   */
  private cleanupExpiredEvents(): void {
    const events = this.getPurchaseEventsSent();
    const now = Date.now();
    let hasChanges = false;

    Object.keys(events).forEach(eventId => {
      if (now - events[eventId] > this.EVENT_EXPIRATION_TIME) {
        delete events[eventId];
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.savePurchaseEventsSent(events);
    }
  }

  /**
   * Verifica se um evento já foi enviado (combinando localStorage e memória de sessão)
   */
  private isEventAlreadySent(eventKey: string): boolean {
    // Verifica primeiro na memória da sessão para maior performance
    if (this.eventsSentInSession.has(eventKey)) {
      return true;
    }

    // Depois verifica no localStorage
    const events = this.getPurchaseEventsSent();
    return !!events[eventKey];
  }

  /**
   * Marca um evento como enviado (tanto no localStorage quanto na memória de sessão)
   */
  private markEventAsSent(eventKey: string): void {
    // Marca na memória da sessão
    this.eventsSentInSession.add(eventKey);

    // Marca no localStorage
    const events = this.getPurchaseEventsSent();
    events[eventKey] = Date.now();
    this.savePurchaseEventsSent(events);
  }

  /**
   * Rastreia um evento de compra uma única vez por pedido, com persistência entre sessões
   * @param pedido O objeto do pedido
   * @param codigoPedido Código do pedido
   * @param idPedido ID do pedido
   * @param guidPedido GUID do pedido
   */
  trackPurchase(pedido: any, codigoPedido: string, idPedido: any, guidPedido: string): void {
    if (!codigoPedido || !pedido) {
      console.error('Pedido ou código de pedido inválido');
      return;
    }

    // Chave única para este pedido (combinando código e guid para garantir unicidade)
    const eventKey = `purchase_${codigoPedido}_${guidPedido}`;

    // Verifica se já enviamos este evento
    if (this.isEventAlreadySent(eventKey)) {
      console.log(`Evento de compra já enviado para o pedido ${codigoPedido}. Ignorando.`);
      return;
    }

    // Marca como enviado imediatamente para evitar chamadas duplicadas
    this.markEventAsSent(eventKey);

    // Rastreamento do Facebook Pixel
    if (typeof fbq !== 'undefined') {
      this.autorizacaoLojaService.obtenhaIdSessao().then((idSessao: string) => {
        // Novo formato de ID de evento
        const idEvento = idSessao + '-purchase-' + codigoPedido + '-' + new Date().getTime();

        fbq('track', 'Purchase', {
          value: pedido.total,
          currency: 'BRL',
          order_id: idPedido
        }, {
          eventID: idEvento
        });

        console.log(`Evento de compra do Facebook Pixel enviado para o pedido ${codigoPedido} com eventID: ${idEvento}`);

        // Enviar evento para a API de Conversões do Facebook através do back-end
        this.carrinhoService.envieEventoCompra({
          pedidoId: idPedido,
          pedidoCodigo: codigoPedido,
          eventId: idEvento,
          valor: pedido.total
        }).then(() => {
          console.log(`Evento de compra enviado para a API de Conversões do Facebook para o pedido ${codigoPedido}`);
        }).catch(error => {
          console.error(`Erro ao enviar evento de compra para a API de Conversões do Facebook: ${error}`);
        });
      });
    }

    // Rastreamento do Google Analytics
    if (typeof gtag !== 'undefined') {
      const items = pedido.itens.map(item => {
        return {
          id: item.produto.id,
          name: item.produto.nome,
          category: item.produto.categoria ? item.produto.categoria.nome : 'Sem Categoria',
          quantity: item.qtde,
          price: item.total / item.qtde
        }
      });

      gtag('event', 'purchase', {
        currency: 'BRL',
        transaction_id: guidPedido,
        value: pedido.total,
        items: items
      });

      console.log(`Evento de compra do Google Analytics enviado para o pedido ${codigoPedido}`);
    }
  }

  /**
   * Rastreia evento InitiateCheckout uma única vez por pedido
   * @param pedido O objeto do pedido
   * @param idEvento ID do evento para deduplicação
   */
  trackInitiateCheckout(pedido: any, idEvento: string): void {
    if (typeof fbq !== 'undefined') {
      fbq('track', 'InitiateCheckout', {
        value: pedido.total,
        currency: 'BRL',
      }, {
        eventID: idEvento
      });
    }
  }

  /**
   * Rastreia evento AddPaymentInfo
   * @param idEvento ID do evento para deduplicação
   */
  trackAddPaymentInfo(idEvento: string): void {
    if (typeof fbq !== 'undefined') {
      fbq('track', 'AddPaymentInfo', {}, {
        eventID: idEvento
      });
    }
  }

  /**
   * Rastreia evento AddToCart e AddedToCart
   * @param item O item sendo adicionado
   * @param pedido O pedido atual
   */
  trackAddToCart(item: any, pedido: any): void {
    if (typeof fbq !== 'undefined') {
      this.autorizacaoLojaService.obtenhaIdSessao().then((idSessao: string) => {
        // Usar o mesmo formato de ID que o back-end usa para AddToCart
        const idEventoAddToCart = idSessao + '-' + item.produto.id + new Date().getTime();

        // Rastreia AddToCart para cada item adicionado
        fbq('track', 'AddToCart', {
          value: item.total,
          currency: 'BRL',
          content_ids: [item.produto.id],
          content_name: item.produto.nome,
          content_type: 'product',
          contents: [{
            id: item.produto.id,
            quantity: item.qtde,
            item_price: item.total / item.qtde
          }]
        }, {
          eventID: idEventoAddToCart
        });

        // Envia o evento para o servidor com o mesmo ID
        const itemComEventoId = {...item, idEvento: idEventoAddToCart};
        this.carrinhoService.notifiqueAdicionouCarrinho(itemComEventoId);

        // Rastreia AddedToCart apenas uma vez por pedido
        if (!pedido.disparouEventoAddedToCart) {
          pedido.disparouEventoAddedToCart = true;
          const idEventoAddedToCart = idEventoAddToCart + '--added';

          fbq('trackCustom', 'AddedToCart', {
            content_ids: [item.produto.id],
            content_type: 'product',
            content_name: item.produto.nome,
            value: item.total / item.qtde,
            contents: [{
              id: item.produto.id,
              quantity: item.qtde
            }],
            currency: 'BRL',
          }, {
            eventID: idEventoAddedToCart
          });
        }
      });
    }
  }
}
