 High            axios Inefficient Regular Expression Complexity
                  vulnerability

  Package         axios

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gcp-metadata >
                  axios

  More info       https://github.com/advisories/GHSA-cph5-m8f7-6c5x




  High            Prototype Pollution in node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-92xj-mqp7-vmcj




  High            Prototype Pollution in node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > google-auth-library >
                  gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-92xj-mqp7-vmcj




  High            Prototype Pollution in node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > googleapis-common >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-92xj-mqp7-vmcj




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > google-auth-library >
                  gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > googleapis-common >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > google-auth-library >
                  gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > googleapis-common >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > axios >
                  follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q




  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gcp-metadata >
                  axios > follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > google-auth-library >
                  semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > axios >
                  follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc




  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gcp-metadata >
                  axios > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc



# Run  npm install dialogflow@4.0.3  to resolve 9 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Low             Prototype Pollution in node-forge debug API.

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-5rrq-pxf6-6jx5




  Low             Prototype Pollution in node-forge util.setPath API

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-wxgw-qj99-44c2




  Low             URL parsing in node-forge could lead to undesired behavior.

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-gf8q-jrpm-jvxq




  Moderate        Improper Verification of Cryptographic Signature in
                  `node-forge`

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-2r2c-g63r-vccr




  Moderate        Open Redirect in node-forge

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-8fr3-hfg3-gpgp




  High            Prototype Pollution in node-forge

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-92xj-mqp7-vmcj




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gtoken >
                  google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   dialogflow

  Path            dialogflow > google-gax > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install firebase-admin@12.0.0  to resolve 25 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Low             Prototype Pollution in node-forge debug API.

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-5rrq-pxf6-6jx5




  Low             Prototype Pollution in node-forge debug API.

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-5rrq-pxf6-6jx5




  Low             Prototype Pollution in node-forge util.setPath API

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-wxgw-qj99-44c2




  Low             Prototype Pollution in node-forge util.setPath API

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-wxgw-qj99-44c2




  Low             URL parsing in node-forge could lead to undesired behavior.

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-gf8q-jrpm-jvxq




  Low             URL parsing in node-forge could lead to undesired behavior.

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-gf8q-jrpm-jvxq




  Moderate        Improper Verification of Cryptographic Signature in
                  `node-forge`

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-2r2c-g63r-vccr




  Moderate        Improper Verification of Cryptographic Signature in
                  `node-forge`

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-2r2c-g63r-vccr




  Moderate        jsonwebtoken unrestricted key type could lead to legacy keys
                  usage

  Package         jsonwebtoken

  Dependency of   firebase-admin

  Path            firebase-admin > jsonwebtoken

  More info       https://github.com/advisories/GHSA-8cf7-32gw-wr33




  Moderate        Uncontrolled Resource Consumption in firebase

  Package         @firebase/util

  Dependency of   firebase-admin

  Path            firebase-admin > @firebase/database > @firebase/util

  More info       https://github.com/advisories/GHSA-fpm5-vv97-jfwg




  Moderate        Uncontrolled Resource Consumption in firebase

  Package         @firebase/util

  Dependency of   firebase-admin

  Path            firebase-admin > @firebase/database > @firebase/component >
                  @firebase/util

  More info       https://github.com/advisories/GHSA-fpm5-vv97-jfwg




  Moderate        jsonwebtoken's insecure implementation of key retrieval
                  function could lead to Forgeable Public/Private Tokens from
                  RSA to HMAC

  Package         jsonwebtoken

  Dependency of   firebase-admin

  Path            firebase-admin > jsonwebtoken

  More info       https://github.com/advisories/GHSA-hjrf-2m68-5959




  Moderate        jsonwebtoken vulnerable to signature validation bypass due
                  to insecure default algorithm in jwt.verify()

  Package         jsonwebtoken

  Dependency of   firebase-admin

  Path            firebase-admin > jsonwebtoken

  More info       https://github.com/advisories/GHSA-qwph-4952-7xr6




  Moderate        Open Redirect in node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-8fr3-hfg3-gpgp




  Moderate        Open Redirect in node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-8fr3-hfg3-gpgp




  High            Prototype Pollution in node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-92xj-mqp7-vmcj




  High            Prototype Pollution in node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-92xj-mqp7-vmcj




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  google-auth-library > gtoken > google-p12-pem > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   firebase-admin

  Path            firebase-admin > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  Moderate        Logging of the firestore key within nodejs-firestore

  Package         @google-cloud/firestore

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore

  More info       https://github.com/advisories/GHSA-4g6q-77j7-vvjc




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   firebase-admin

  Path            firebase-admin > jsonwebtoken > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/storage >
                  gcs-resumable-upload > configstore > make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install --save-dev @angular-devkit/build-angular@17.1.3  to resolve 159 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Low             Prototype Pollution in node-forge debug API.

  Package         node-forge

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  selfsigned > node-forge

  More info       https://github.com/advisories/GHSA-5rrq-pxf6-6jx5




  Low             URL parsing in node-forge could lead to undesired behavior.

  Package         node-forge

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  selfsigned > node-forge

  More info       https://github.com/advisories/GHSA-gf8q-jrpm-jvxq




  Moderate        Improper Verification of Cryptographic Signature in
                  `node-forge`

  Package         node-forge

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  selfsigned > node-forge

  More info       https://github.com/advisories/GHSA-2r2c-g63r-vccr




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > css-declaration-sorter > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > cssnano-util-raw-cache > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-calc > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-colormin > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-convert-values > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-comments > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-duplicates >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-empty > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-overridden >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-longhand > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-longhand > stylehacks
                  > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-rules > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-font-values >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-gradients > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-params > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-selectors > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-charset > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-display-values >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-positions >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-repeat-style >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-string > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-timing-functions
                  > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-unicode > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-url > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-whitespace >
                  postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-ordered-values > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-reduce-initial > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-reduce-transforms > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-svgo > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-unique-selectors > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        ReDoS in Sec-Websocket-Protocol header

  Package         ws

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > ws

  More info       https://github.com/advisories/GHSA-6fc8-4gx4-v693




  High            Terser insecure use of regular expressions leads to ReDoS

  Package         terser

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > terser

  More info       https://github.com/advisories/GHSA-4wf5-vphf-c2xc




  High            Terser insecure use of regular expressions leads to ReDoS

  Package         terser

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > terser

  More info       https://github.com/advisories/GHSA-4wf5-vphf-c2xc




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > css-declaration-sorter > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > cssnano-util-raw-cache > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-calc > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-colormin > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-convert-values > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-comments > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-duplicates >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-empty > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-overridden >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-longhand > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-longhand > stylehacks
                  > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-rules > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-font-values >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-gradients > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-params > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-selectors > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-charset > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-display-values >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-positions >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-repeat-style >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-string > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-timing-functions
                  > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-unicode > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-url > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-whitespace >
                  postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-ordered-values > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-reduce-initial > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-reduce-transforms > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-svgo > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-unique-selectors > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        Open Redirect in node-forge

  Package         node-forge

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  selfsigned > node-forge

  More info       https://github.com/advisories/GHSA-8fr3-hfg3-gpgp




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @angular-devkit/build-optimizer > loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > babel-loader > loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > worker-plugin > loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > critters > css >
                  source-map-resolve > decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader > rework
                  > css > source-map-resolve > decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > micromatch >
                  braces > snapdragon > source-map-resolve >
                  decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > micromatch >
                  extglob > expand-brackets > snapdragon > source-map-resolve
                  > decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  http-proxy-middleware > micromatch > extglob >
                  expand-brackets > snapdragon > source-map-resolve >
                  decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  chokidar > anymatch > micromatch > extglob > expand-brackets
                  > snapdragon > source-map-resolve > decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > watchpack >
                  watchpack-chokidar2 > chokidar > anymatch > micromatch >
                  extglob > snapdragon > source-map-resolve >
                  decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  High            decode-uri-component vulnerable to Denial of Service (DoS)

  Package         decode-uri-component

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > watchpack >
                  watchpack-chokidar2 > chokidar > anymatch > micromatch >
                  extglob > expand-brackets > snapdragon > source-map-resolve
                  > decode-uri-component

  More info       https://github.com/advisories/GHSA-w573-4hg7-7wgq




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > babel-loader > loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > worker-plugin > loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @angular-devkit/build-optimizer > loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > yargs >
                  string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > yargs >
                  cliui > string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > yargs >
                  cliui > wrap-ansi > string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > css-declaration-sorter > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > cssnano-util-raw-cache > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-calc > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-colormin > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-convert-values > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-comments > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-duplicates >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-empty > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-discard-overridden >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-longhand > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-longhand > stylehacks
                  > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-merge-rules > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-font-values >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-gradients > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-params > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-minify-selectors > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-charset > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-display-values >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-positions >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-repeat-style >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-string > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-timing-functions
                  > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-unicode > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-url > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-normalize-whitespace >
                  postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-ordered-values > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-reduce-initial > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-reduce-transforms > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-svgo > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-unique-selectors > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j




  High            Uncontrolled Resource Consumption in ansi-html

  Package         ansi-html

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  ansi-html

  More info       https://github.com/advisories/GHSA-whgm-jr23-g3j9




  High            glob-parent vulnerable to Regular Expression Denial of
                  Service in enclosure regex

  Package         glob-parent

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > watchpack >
                  watchpack-chokidar2 > chokidar > glob-parent

  More info       https://github.com/advisories/GHSA-ww39-953v-wcq6




  High            glob-parent vulnerable to Regular Expression Denial of
                  Service in enclosure regex

  Package         glob-parent

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  chokidar > glob-parent

  More info       https://github.com/advisories/GHSA-ww39-953v-wcq6




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  selfsigned > node-forge

  More info       https://github.com/advisories/GHSA-cfm4-qjh2-4765




  High            Improper Verification of Cryptographic Signature in
                  node-forge

  Package         node-forge

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  selfsigned > node-forge

  More info       https://github.com/advisories/GHSA-x4jg-mjrx-434g




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @angular-devkit/build-optimizer > loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > babel-loader > loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > worker-plugin > loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488




  High            Inefficient Regular Expression Complexity in nth-check

  Package         nth-check

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-svgo > svgo > css-select >
                  nth-check

  More info       https://github.com/advisories/GHSA-rp65-9cf3-cjxr




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > find-cache-dir > make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > find-cache-dir > make-dir >
                  semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > babel-loader >
                  find-cache-dir > make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > @babel/preset-env >
                  @babel/plugin-proposal-object-rest-spread >
                  @babel/helper-compilation-targets > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > stylus > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > babel-loader > loader-utils
                  > json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h




  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  loader-utils > json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h




  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > loader-utils >
                  json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h




  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > worker-plugin > loader-utils
                  > json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h




  High            Prototype Pollution in async

  Package         async

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  portfinder > async

  More info       https://github.com/advisories/GHSA-fwr7-v2mv-hh25



# Run  npm install mercadopago@2.0.7  to resolve 3 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Dependency of   mercadopago

  Path            mercadopago > request-etag > request > har-validator > ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw




  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Dependency of   mercadopago

  Path            mercadopago > ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw




  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Dependency of   mercadopago

  Path            mercadopago > request-etag > request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3



# Run  npm install email-templates@11.1.1  to resolve 17 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Dependency of   email-templates

  Path            email-templates > juice > web-resource-inliner > request >
                  har-validator > ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw




  Moderate        Header injection in nodemailer

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > nodemailer

  More info       https://github.com/advisories/GHSA-hwqf-gcqm-7353




  Moderate        Header injection in nodemailer

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > preview-email > nodemailer

  More info       https://github.com/advisories/GHSA-hwqf-gcqm-7353




  Moderate        Header injection in nodemailer

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > preview-email > mailparser > nodemailer

  More info       https://github.com/advisories/GHSA-hwqf-gcqm-7353




  Critical        Command injection in nodemailer

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > nodemailer

  More info       https://github.com/advisories/GHSA-48ww-j4fc-435p




  Critical        Command injection in nodemailer

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > preview-email > nodemailer

  More info       https://github.com/advisories/GHSA-48ww-j4fc-435p




  Critical        Command injection in nodemailer

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > preview-email > mailparser > nodemailer

  More info       https://github.com/advisories/GHSA-48ww-j4fc-435p




  High            Remote code execution via the `pretty` option.

  Package         pug

  Dependency of   email-templates

  Path            email-templates > preview-email > pug

  More info       https://github.com/advisories/GHSA-p493-635q-r6gr




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   email-templates

  Path            email-templates > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   email-templates

  Path            email-templates > preview-email > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Critical        Arbitrary Code Execution in underscore

  Package         underscore

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > country-language >
                  underscore

  More info       https://github.com/advisories/GHSA-cf4h-3jhx-xvhq




  Critical        Arbitrary Code Execution in underscore

  Package         underscore

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > i18n-locales >
                  country-language > underscore

  More info       https://github.com/advisories/GHSA-cf4h-3jhx-xvhq




  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Dependency of   email-templates

  Path            email-templates > juice > web-resource-inliner > request >
                  tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3




  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   email-templates

  Path            email-templates > juice > web-resource-inliner > request >
                  qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp




  Moderate        nodemailer ReDoS when trying to send a specially crafted
                  email

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > nodemailer

  More info       https://github.com/advisories/GHSA-9h6g-pr28-7cqp




  Moderate        nodemailer ReDoS when trying to send a specially crafted
                  email

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > preview-email > nodemailer

  More info       https://github.com/advisories/GHSA-9h6g-pr28-7cqp




  Moderate        nodemailer ReDoS when trying to send a specially crafted
                  email

  Package         nodemailer

  Dependency of   email-templates

  Path            email-templates > preview-email > mailparser > nodemailer

  More info       https://github.com/advisories/GHSA-9h6g-pr28-7cqp



# Run  npm install --save-dev @angular/cli@17.1.3  to resolve 9 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp >
                  request > har-validator > ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw




  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > request > har-validator >
                  ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw




  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp >
                  request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3




  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3




  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > universal-analytics > request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp >
                  request > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp




  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > request > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp



# Run  npm install --save-dev mocha@10.3.0  to resolve 8 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Critical        flat vulnerable to Prototype Pollution

  Package         flat

  Dependency of   mocha [dev]

  Path            mocha > yargs-unparser > flat

  More info       https://github.com/advisories/GHSA-2j2x-2gpw-g8fm




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   mocha [dev]

  Path            mocha > yargs > string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   mocha [dev]

  Path            mocha > yargs > cliui > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   mocha [dev]

  Path            mocha > yargs > cliui > wrap-ansi > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   mocha [dev]

  Path            mocha > yargs-unparser > yargs > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   mocha [dev]

  Path            mocha > yargs-unparser > yargs > cliui > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   mocha [dev]

  Path            mocha > yargs-unparser > yargs > cliui > wrap-ansi >
                  string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   mocha [dev]

  Path            mocha > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c



# Run  npm install connect-redis@7.1.1  to resolve 1 vulnerability
SEMVER WARNING: Recommended action is a potentially breaking change

  High            Node-Redis potential exponential regex in monitor mode

  Package         redis

  Dependency of   connect-redis

  Path            connect-redis > redis

  More info       https://github.com/advisories/GHSA-35q2-47q7-3pc3



# Run  npm install ejs@3.1.9  to resolve 1 vulnerability
SEMVER WARNING: Recommended action is a potentially breaking change

  Critical        ejs template injection vulnerability

  Package         ejs

  Dependency of   ejs

  Path            ejs

  More info       https://github.com/advisories/GHSA-phwq-j96m-2c2q



# Run  npm install @progress/kendo-licensing@1.3.5  to resolve 4 vulnerabilities

  Moderate        jsonwebtoken unrestricted key type could lead to legacy keys
                  usage

  Package         jsonwebtoken

  Dependency of   @progress/kendo-licensing

  Path            @progress/kendo-licensing > jsonwebtoken

  More info       https://github.com/advisories/GHSA-8cf7-32gw-wr33




  Moderate        jsonwebtoken's insecure implementation of key retrieval
                  function could lead to Forgeable Public/Private Tokens from
                  RSA to HMAC

  Package         jsonwebtoken

  Dependency of   @progress/kendo-licensing

  Path            @progress/kendo-licensing > jsonwebtoken

  More info       https://github.com/advisories/GHSA-hjrf-2m68-5959




  Moderate        jsonwebtoken vulnerable to signature validation bypass due
                  to insecure default algorithm in jwt.verify()

  Package         jsonwebtoken

  Dependency of   @progress/kendo-licensing

  Path            @progress/kendo-licensing > jsonwebtoken

  More info       https://github.com/advisories/GHSA-qwph-4952-7xr6




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @progress/kendo-licensing

  Path            @progress/kendo-licensing > jsonwebtoken > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install express-jwt@8.4.1  to resolve 4 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        jsonwebtoken unrestricted key type could lead to legacy keys
                  usage

  Package         jsonwebtoken

  Dependency of   express-jwt

  Path            express-jwt > jsonwebtoken

  More info       https://github.com/advisories/GHSA-8cf7-32gw-wr33




  High            Authorization bypass in express-jwt

  Package         express-jwt

  Dependency of   express-jwt

  Path            express-jwt

  More info       https://github.com/advisories/GHSA-6g6m-m6h5-w9gf




  Moderate        jsonwebtoken's insecure implementation of key retrieval
                  function could lead to Forgeable Public/Private Tokens from
                  RSA to HMAC

  Package         jsonwebtoken

  Dependency of   express-jwt

  Path            express-jwt > jsonwebtoken

  More info       https://github.com/advisories/GHSA-hjrf-2m68-5959




  Moderate        jsonwebtoken vulnerable to signature validation bypass due
                  to insecure default algorithm in jwt.verify()

  Package         jsonwebtoken

  Dependency of   express-jwt

  Path            express-jwt > jsonwebtoken

  More info       https://github.com/advisories/GHSA-qwph-4952-7xr6



# Run  npm install twilio@4.21.0  to resolve 4 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        jsonwebtoken unrestricted key type could lead to legacy keys
                  usage

  Package         jsonwebtoken

  Dependency of   twilio

  Path            twilio > jsonwebtoken

  More info       https://github.com/advisories/GHSA-8cf7-32gw-wr33




  Moderate        jsonwebtoken's insecure implementation of key retrieval
                  function could lead to Forgeable Public/Private Tokens from
                  RSA to HMAC

  Package         jsonwebtoken

  Dependency of   twilio

  Path            twilio > jsonwebtoken

  More info       https://github.com/advisories/GHSA-hjrf-2m68-5959




  Moderate        jsonwebtoken vulnerable to signature validation bypass due
                  to insecure default algorithm in jwt.verify()

  Package         jsonwebtoken

  Dependency of   twilio

  Path            twilio > jsonwebtoken

  More info       https://github.com/advisories/GHSA-qwph-4952-7xr6




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   twilio

  Path            twilio > jsonwebtoken > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install --save-dev karma@6.4.2  to resolve 4 vulnerabilities

  Moderate        Uncaught exception in engine.io

  Package         engine.io

  Dependency of   karma [dev]

  Path            karma > socket.io > engine.io

  More info       https://github.com/advisories/GHSA-r7qp-cfhv-p84w




  High            Uncaught Exception in engine.io

  Package         engine.io

  Dependency of   karma [dev]

  Path            karma > socket.io > engine.io

  More info       https://github.com/advisories/GHSA-273r-mgr4-v34f




  High            Insufficient validation when decoding a Socket.IO packet

  Package         socket.io-parser

  Dependency of   karma [dev]

  Path            karma > socket.io > socket.io-parser

  More info       https://github.com/advisories/GHSA-cqmj-92xf-r6r9




  Critical        Insufficient validation when decoding a Socket.IO packet

  Package         socket.io-parser

  Dependency of   karma [dev]

  Path            karma > socket.io > socket.io-parser

  More info       https://github.com/advisories/GHSA-qm95-pgcg-qqfq



# Run  npm install bcrypt@5.1.1  to resolve 5 vulnerabilities

  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p



# Run  npm install console-stamp@3.1.2  to resolve 3 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  High            Prototype Pollution in merge

  Package         merge

  Dependency of   console-stamp

  Path            console-stamp > merge

  More info       https://github.com/advisories/GHSA-7wpw-2hjm-89gp




  High            Uncontrolled Resource Consumption in trim-newlines

  Package         trim-newlines

  Dependency of   console-stamp

  Path            console-stamp > dateformat > meow > trim-newlines

  More info       https://github.com/advisories/GHSA-7p7h-4mm5-852v




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   console-stamp

  Path            console-stamp > dateformat > meow > read-pkg-up > read-pkg >
                  normalize-package-data > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install sharp@0.33.2  to resolve 3 vulnerabilities

  High            Exposure of Sensitive Information in simple-get

  Package         simple-get

  Dependency of   sharp

  Path            sharp > prebuild-install > simple-get

  More info       https://github.com/advisories/GHSA-wpg7-2c88-r8xv




  Moderate        sharp vulnerable to Command Injection in post-installation
                  over build environment

  Package         sharp

  Dependency of   sharp

  Path            sharp

  More info       https://github.com/advisories/GHSA-gp95-ppv5-3jc5




  High            sharp vulnerability in libwebp dependency CVE-2023-4863

  Package         sharp

  Dependency of   sharp

  Path            sharp

  More info       https://github.com/advisories/GHSA-54xq-cgqr-rpm3



# Run  npm install puppeteer@22.0.0  to resolve 2 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        ReDoS in Sec-Websocket-Protocol header

  Package         ws

  Dependency of   puppeteer

  Path            puppeteer > ws

  More info       https://github.com/advisories/GHSA-6fc8-4gx4-v693




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   puppeteer

  Path            puppeteer > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c



# Run  npm install aws-sdk@2.1554.0  to resolve 1 vulnerability

  Moderate        xml2js is vulnerable to prototype pollution

  Package         xml2js

  Dependency of   aws-sdk

  Path            aws-sdk > xml2js

  More info       https://github.com/advisories/GHSA-776f-qx25-q3cc



# Run  npm install xml2js@0.6.2  to resolve 1 vulnerability

  Moderate        xml2js is vulnerable to prototype pollution

  Package         xml2js

  Dependency of   xml2js

  Path            xml2js

  More info       https://github.com/advisories/GHSA-776f-qx25-q3cc



# Run  npm install passport@0.7.0  to resolve 1 vulnerability

  Moderate        Passport vulnerable to session regeneration when a users
                  logs in or out

  Package         passport

  Dependency of   passport

  Path            passport

  More info       https://github.com/advisories/GHSA-v923-w3x8-wh69



# Run  npm install concurrently@8.2.2  to resolve 3 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   concurrently

  Path            concurrently > yargs > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   concurrently

  Path            concurrently > yargs > cliui > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   concurrently

  Path            concurrently > yargs > cliui > wrap-ansi > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw



# Run  npm install axios@1.6.7  to resolve 2 vulnerabilities
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        Axios Cross-Site Request Forgery Vulnerability

  Package         axios

  Dependency of   axios

  Path            axios

  More info       https://github.com/advisories/GHSA-wf5p-g6vw-rhxx




  High            axios Inefficient Regular Expression Complexity
                  vulnerability

  Package         axios

  Dependency of   axios

  Path            axios

  More info       https://github.com/advisories/GHSA-cph5-m8f7-6c5x



# Run  npm install openai@4.26.1  to resolve 1 vulnerability
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        Axios Cross-Site Request Forgery Vulnerability

  Package         axios

  Dependency of   openai

  Path            openai > axios

  More info       https://github.com/advisories/GHSA-wf5p-g6vw-rhxx



# Run  npm install bull@4.12.2  to resolve 1 vulnerability
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   bull

  Path            bull > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install --save-dev @angular/compiler-cli@17.1.3  to resolve 1 vulnerability
SEMVER WARNING: Recommended action is a potentially breaking change

  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/compiler-cli [dev]

  Path            @angular/compiler-cli > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm install express@4.18.2  to resolve 2 vulnerabilities

  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   express

  Path            express > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp




  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   express

  Path            express > body-parser > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp



# Run  npm install @mapbox/togeojson@0.16.2  to resolve 2 vulnerabilities

  Moderate        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @mapbox/togeojson

  Path            @mapbox/togeojson > minimist

  More info       https://github.com/advisories/GHSA-vh95-rmgr-6w4m




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @mapbox/togeojson

  Path            @mapbox/togeojson > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h



# Run  npm update stream-transform --depth 2  to resolve 3 vulnerabilities

  High            Prototype Pollution in mixme

  Package         mixme

  Dependency of   csv

  Path            csv > stream-transform > mixme

  More info       https://github.com/advisories/GHSA-84p7-fh9c-6g8h




  Critical        Prototype Pollution in mixme

  Package         mixme

  Dependency of   csv

  Path            csv > stream-transform > mixme

  More info       https://github.com/advisories/GHSA-r5cq-9537-9rpf




  High            Use of Potentially Dangerous Function in mixme

  Package         mixme

  Dependency of   csv

  Path            csv > stream-transform > mixme

  More info       https://github.com/advisories/GHSA-79jw-6wg7-r9g4



# Run  npm update websocket-extensions --depth 6  to resolve 2 vulnerabilities

  High            Regular Expression Denial of Service in websocket-extensions
                  (NPM package)

  Package         websocket-extensions

  Dependency of   firebase-admin

  Path            firebase-admin > @firebase/database > faye-websocket >
                  websocket-driver > websocket-extensions

  More info       https://github.com/advisories/GHSA-g78m-2chm-r7qv




  High            Regular Expression Denial of Service in websocket-extensions
                  (NPM package)

  Package         websocket-extensions

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > faye-websocket > websocket-driver >
                  websocket-extensions

  More info       https://github.com/advisories/GHSA-g78m-2chm-r7qv



# Run  npm update moment-timezone --depth 3  to resolve 4 vulnerabilities

  Low             Command Injection in moment-timezone

  Package         moment-timezone

  Dependency of   cron

  Path            cron > moment-timezone

  More info       https://github.com/advisories/GHSA-56x4-j7p9-fcf9




  Low             Command Injection in moment-timezone

  Package         moment-timezone

  Dependency of   bull

  Path            bull > cron-parser > moment-timezone

  More info       https://github.com/advisories/GHSA-56x4-j7p9-fcf9




  Moderate        Cleartext Transmission of Sensitive Information in
                  moment-timezone

  Package         moment-timezone

  Dependency of   cron

  Path            cron > moment-timezone

  More info       https://github.com/advisories/GHSA-v78c-4p63-2j6c




  Moderate        Cleartext Transmission of Sensitive Information in
                  moment-timezone

  Package         moment-timezone

  Dependency of   bull

  Path            bull > cron-parser > moment-timezone

  More info       https://github.com/advisories/GHSA-v78c-4p63-2j6c



# Run  npm update nanoid --depth 4  to resolve 3 vulnerabilities

  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in nanoid

  Package         nanoid

  Dependency of   nanoid

  Path            nanoid

  More info       https://github.com/advisories/GHSA-qrpm-p2h7-hrv2




  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in nanoid

  Package         nanoid

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > postcss > nanoid

  More info       https://github.com/advisories/GHSA-qrpm-p2h7-hrv2




  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in nanoid

  Package         nanoid

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > css-loader > postcss >
                  nanoid

  More info       https://github.com/advisories/GHSA-qrpm-p2h7-hrv2



# Run  npm update chart.js --depth 1  to resolve 1 vulnerability

  High            Prototype pollution in chart.js

  Package         chart.js

  Dependency of   chart.js

  Path            chart.js

  More info       https://github.com/advisories/GHSA-h68q-55jf-x68w



# Run  npm update google-gax --depth 3  to resolve 4 vulnerabilities

  High            Prototype pollution in grpc and @grpc/grpc-js

  Package         @grpc/grpc-js

  Dependency of   dialogflow

  Path            dialogflow > google-gax > @grpc/grpc-js

  More info       https://github.com/advisories/GHSA-pp75-xfpw-37g9




  High            Prototype pollution in grpc and @grpc/grpc-js

  Package         @grpc/grpc-js

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  @grpc/grpc-js

  More info       https://github.com/advisories/GHSA-pp75-xfpw-37g9




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   dialogflow

  Path            dialogflow > google-gax > @grpc/grpc-js > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  @grpc/grpc-js > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm update aws-sdk --depth 1  to resolve 1 vulnerability

  High            Prototype Pollution via file load in aws-sdk and
                  @aws-sdk/shared-ini-file-loader

  Package         aws-sdk

  Dependency of   aws-sdk

  Path            aws-sdk

  More info       https://github.com/advisories/GHSA-rrc9-gqf8-8rwg



# Run  npm update path-parse --depth 6  to resolve 4 vulnerabilities

  Moderate        Regular Expression Denial of Service in path-parse

  Package         path-parse

  Dependency of   tslint [dev]

  Path            tslint > resolve > path-parse

  More info       https://github.com/advisories/GHSA-hj48-42vr-x3v9




  Moderate        Regular Expression Denial of Service in path-parse

  Package         path-parse

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > resolve > path-parse

  More info       https://github.com/advisories/GHSA-hj48-42vr-x3v9




  Moderate        Regular Expression Denial of Service in path-parse

  Package         path-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > resolve > path-parse

  More info       https://github.com/advisories/GHSA-hj48-42vr-x3v9




  Moderate        Regular Expression Denial of Service in path-parse

  Package         path-parse

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > resolve > path-parse

  More info       https://github.com/advisories/GHSA-hj48-42vr-x3v9



# Run  npm update tar --depth 7  to resolve 45 vulnerabilities

  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cacache > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > copy-webpack-plugin >
                  cacache > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > cacache > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-registry-fetch >
                  make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite due to insufficient
                  absolute path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > tar

  More info       https://github.com/advisories/GHSA-3jfq-g458-7qm9




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cacache > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > copy-webpack-plugin >
                  cacache > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > cacache > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-registry-fetch >
                  make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite on Windows via
                  insufficient relative path sanitization

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > tar

  More info       https://github.com/advisories/GHSA-5955-9wpr-37jh




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cacache > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > copy-webpack-plugin >
                  cacache > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > cacache > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-registry-fetch >
                  make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > tar

  More info       https://github.com/advisories/GHSA-r628-mhmh-qjhw




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cacache > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > copy-webpack-plugin >
                  cacache > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > cacache > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-registry-fetch >
                  make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > tar

  More info       https://github.com/advisories/GHSA-9r2w-394v-53qc




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cacache > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > copy-webpack-plugin >
                  cacache > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > cacache > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-registry-fetch >
                  make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen > cacache > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p




  High            Arbitrary File Creation/Overwrite via insufficient symlink
                  protection due to directory cache poisoning using symbolic
                  links

  Package         tar

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > tar

  More info       https://github.com/advisories/GHSA-qq89-hq3f-393p



# Run  npm update color-string --depth 6  to resolve 2 vulnerabilities

  Moderate        Regular Expression Denial of Service (ReDOS)

  Package         color-string

  Dependency of   sharp

  Path            sharp > color > color-string

  More info       https://github.com/advisories/GHSA-257v-vj4p-3w2h




  Moderate        Regular Expression Denial of Service (ReDOS)

  Package         color-string

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-colormin > color >
                  color-string

  More info       https://github.com/advisories/GHSA-257v-vj4p-3w2h



# Run  npm update postcss --depth 3  to resolve 3 vulnerabilities

  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > css-loader > postcss

  More info       https://github.com/advisories/GHSA-hwj9-h5mp-3pm3




  Moderate        Regular Expression Denial of Service in postcss

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > css-loader > postcss

  More info       https://github.com/advisories/GHSA-566m-qj78-rww5




  Moderate        PostCSS line return parsing error

  Package         postcss

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > css-loader > postcss

  More info       https://github.com/advisories/GHSA-7fh5-64p2-3v2j



# Run  npm update hosted-git-info --depth 7  to resolve 3 vulnerabilities

  Moderate        Regular Expression Denial of Service in hosted-git-info

  Package         hosted-git-info

  Dependency of   concurrently

  Path            concurrently > read-pkg > normalize-package-data >
                  hosted-git-info

  More info       https://github.com/advisories/GHSA-43f8-2h32-f4cj




  Moderate        Regular Expression Denial of Service in hosted-git-info

  Package         hosted-git-info

  Dependency of   console-stamp

  Path            console-stamp > dateformat > meow > normalize-package-data >
                  hosted-git-info

  More info       https://github.com/advisories/GHSA-43f8-2h32-f4cj




  Moderate        Regular Expression Denial of Service in hosted-git-info

  Package         hosted-git-info

  Dependency of   console-stamp

  Path            console-stamp > dateformat > meow > read-pkg-up > read-pkg >
                  normalize-package-data > hosted-git-info

  More info       https://github.com/advisories/GHSA-43f8-2h32-f4cj



# Run  npm update pathval --depth 2  to resolve 1 vulnerability

  High            Prototype pollution in pathval

  Package         pathval

  Dependency of   chai [dev]

  Path            chai > pathval

  More info       https://github.com/advisories/GHSA-g6ww-v8xp-vmwg



# Run  npm update pug-code-gen --depth 4  to resolve 1 vulnerability

  High            Remote code execution via the `pretty` option.

  Package         pug-code-gen

  Dependency of   email-templates

  Path            email-templates > preview-email > pug > pug-code-gen

  More info       https://github.com/advisories/GHSA-p493-635q-r6gr



# Run  npm update @google-cloud/storage --depth 2  to resolve 1 vulnerability

  High            regular expression denial of service (ReDoS)

  Package         date-and-time

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/storage > date-and-time

  More info       https://github.com/advisories/GHSA-r92x-f52r-x54g



# Run  npm update karma --depth 1  to resolve 2 vulnerabilities

  Moderate        Open redirect in karma

  Package         karma

  Dependency of   karma [dev]

  Path            karma

  More info       https://github.com/advisories/GHSA-rc3x-jf5g-xvc5




  Moderate        Cross-site Scripting in karma

  Package         karma

  Dependency of   karma [dev]

  Path            karma

  More info       https://github.com/advisories/GHSA-7x7c-qm48-pq9c



# Run  npm update simple-get --depth 2  to resolve 1 vulnerability

  High            Exposure of Sensitive Information in simple-get

  Package         simple-get

  Dependency of   sharp

  Path            sharp > simple-get

  More info       https://github.com/advisories/GHSA-wpg7-2c88-r8xv



# Run  npm update ws --depth 4  to resolve 1 vulnerability

  Moderate        ReDoS in Sec-Websocket-Protocol header

  Package         ws

  Dependency of   karma [dev]

  Path            karma > socket.io > engine.io > ws

  More info       https://github.com/advisories/GHSA-6fc8-4gx4-v693



# Run  npm update url-parse --depth 6  to resolve 10 vulnerabilities

  Moderate        Authorization bypass in url-parse

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > url-parse

  More info       https://github.com/advisories/GHSA-rqff-837h-mm52




  Moderate        Authorization bypass in url-parse

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > eventsource > original > url-parse

  More info       https://github.com/advisories/GHSA-rqff-837h-mm52




  Moderate        Open redirect in url-parse

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > url-parse

  More info       https://github.com/advisories/GHSA-hh27-ffr2-f2jc




  Moderate        Open redirect in url-parse

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > eventsource > original > url-parse

  More info       https://github.com/advisories/GHSA-hh27-ffr2-f2jc




  Moderate        url-parse incorrectly parses hostname / protocol due to
                  unstripped leading control characters.

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > url-parse

  More info       https://github.com/advisories/GHSA-jf5r-8hm2-f872




  Moderate        url-parse incorrectly parses hostname / protocol due to
                  unstripped leading control characters.

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > eventsource > original > url-parse

  More info       https://github.com/advisories/GHSA-jf5r-8hm2-f872




  Moderate        url-parse Incorrectly parses URLs that include an '@'

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > url-parse

  More info       https://github.com/advisories/GHSA-8v38-pw62-9cw2




  Moderate        url-parse Incorrectly parses URLs that include an '@'

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > eventsource > original > url-parse

  More info       https://github.com/advisories/GHSA-8v38-pw62-9cw2




  Critical        Authorization Bypass Through User-Controlled Key in
                  url-parse

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > url-parse

  More info       https://github.com/advisories/GHSA-hgjh-723h-mx2j




  Critical        Authorization Bypass Through User-Controlled Key in
                  url-parse

  Package         url-parse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > eventsource > original > url-parse

  More info       https://github.com/advisories/GHSA-hgjh-723h-mx2j



# Run  npm update jszip --depth 4  to resolve 4 vulnerabilities

  High            JSZip contains Path Traversal via loadAsync

  Package         jszip

  Dependency of   protractor [dev]

  Path            protractor > selenium-webdriver > jszip

  More info       https://github.com/advisories/GHSA-36fh-84j7-cv5h




  High            JSZip contains Path Traversal via loadAsync

  Package         jszip

  Dependency of   protractor [dev]

  Path            protractor > webdriver-js-extender > selenium-webdriver >
                  jszip

  More info       https://github.com/advisories/GHSA-36fh-84j7-cv5h




  Moderate        jszip Vulnerable to Prototype Pollution

  Package         jszip

  Dependency of   protractor [dev]

  Path            protractor > selenium-webdriver > jszip

  More info       https://github.com/advisories/GHSA-jg8v-48h5-wgxg




  Moderate        jszip Vulnerable to Prototype Pollution

  Package         jszip

  Dependency of   protractor [dev]

  Path            protractor > webdriver-js-extender > selenium-webdriver >
                  jszip

  More info       https://github.com/advisories/GHSA-jg8v-48h5-wgxg



# Run  npm update grunt --depth 1  to resolve 2 vulnerabilities

  High            Race Condition in Grunt

  Package         grunt

  Dependency of   grunt [dev]

  Path            grunt

  More info       https://github.com/advisories/GHSA-rm36-94g8-835r




  Moderate        Path Traversal in Grunt

  Package         grunt

  Dependency of   grunt [dev]

  Path            grunt

  More info       https://github.com/advisories/GHSA-j383-35pm-c5h4



# Run  npm update terser --depth 3  to resolve 1 vulnerability

  High            Terser insecure use of regular expressions leads to ReDoS

  Package         terser

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > terser-webpack-plugin >
                  terser

  More info       https://github.com/advisories/GHSA-4wf5-vphf-c2xc



# Run  npm update ua-parser-js --depth 2  to resolve 1 vulnerability

  High            ReDoS Vulnerability in ua-parser-js version

  Package         ua-parser-js

  Dependency of   karma [dev]

  Path            karma > ua-parser-js

  More info       https://github.com/advisories/GHSA-fhg7-m89q-25r3



# Run  npm update http-cache-semantics --depth 6  to resolve 2 vulnerabilities

  High            http-cache-semantics vulnerable to Regular Expression Denial
                  of Service

  Package         http-cache-semantics

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-registry-fetch >
                  make-fetch-happen > http-cache-semantics

  More info       https://github.com/advisories/GHSA-rc47-6667-2j5j




  High            http-cache-semantics vulnerable to Regular Expression Denial
                  of Service

  Package         http-cache-semantics

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen >
                  http-cache-semantics

  More info       https://github.com/advisories/GHSA-rc47-6667-2j5j



# Run  npm update follow-redirects --depth 5  to resolve 10 vulnerabilities

  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in follow-redirects

  Package         follow-redirects

  Dependency of   axios

  Path            axios > follow-redirects

  More info       https://github.com/advisories/GHSA-pw2r-vq6v-hr8c




  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in follow-redirects

  Package         follow-redirects

  Dependency of   @types/axios [dev]

  Path            @types/axios > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-pw2r-vq6v-hr8c




  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in follow-redirects

  Package         follow-redirects

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  http-proxy-middleware > http-proxy > follow-redirects

  More info       https://github.com/advisories/GHSA-pw2r-vq6v-hr8c




  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Dependency of   axios

  Path            axios > follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q




  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Dependency of   @types/axios [dev]

  Path            @types/axios > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q




  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  http-proxy-middleware > http-proxy > follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q




  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Dependency of   axios

  Path            axios > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc




  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Dependency of   @types/axios [dev]

  Path            @types/axios > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc




  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  http-proxy-middleware > http-proxy > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc




  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Dependency of   facebook-nodejs-business-sdk

  Path            facebook-nodejs-business-sdk > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc



# Run  npm update loader-utils --depth 4  to resolve 3 vulnerabilities

  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS)

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  adjust-sourcemap-loader > loader-utils

  More info       https://github.com/advisories/GHSA-hhq3-ff78-jv3g




  Critical        Prototype pollution in webpack loader-utils

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  adjust-sourcemap-loader > loader-utils

  More info       https://github.com/advisories/GHSA-76p3-8jx3-jpfq




  High            loader-utils is vulnerable to Regular Expression Denial of
                  Service (ReDoS) via url variable

  Package         loader-utils

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  adjust-sourcemap-loader > loader-utils

  More info       https://github.com/advisories/GHSA-3rfm-jhwj-7488



# Run  npm update ansi-regex --depth 7  to resolve 13 vulnerabilities

  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/localize

  Path            @angular/localize > yargs > cliui > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/localize

  Path            @angular/localize > yargs > cliui > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/localize

  Path            @angular/localize > yargs > cliui > wrap-ansi > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/localize

  Path            @angular/localize > yargs > cliui > wrap-ansi > string-width
                  > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/localize

  Path            @angular/localize > yargs > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > inquirer > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > inquirer > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > ora > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @angular-devkit/schematics > ora > strip-ansi
                  > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/angular >
                  @angular-devkit/schematics > ora > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   protractor [dev]

  Path            protractor > yargs > string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   protractor [dev]

  Path            protractor > yargs > cliui > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw




  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Dependency of   protractor [dev]

  Path            protractor > yargs > cliui > wrap-ansi > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw



# Run  npm update debug --depth 8  to resolve 9 vulnerabilities

  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   bull

  Path            bull > ioredis > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   connect-redis

  Path            connect-redis > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gaxios >
                  https-proxy-agent > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gaxios >
                  https-proxy-agent > agent-base > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gcp-metadata
                  > gaxios > https-proxy-agent > agent-base > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > i18n > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   puppeteer

  Path            puppeteer > https-proxy-agent > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c




  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Dependency of   puppeteer

  Path            puppeteer > extract-zip > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c



# Run  npm update lodash --depth 8  to resolve 20 vulnerabilities

  High            Command Injection in lodash

  Package         lodash

  Dependency of   bull

  Path            bull > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  http-proxy-middleware > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   email-templates

  Path            email-templates > preview-email > pug > pug-code-gen >
                  constantinople > babel-types > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   email-templates

  Path            email-templates > preview-email > pug > pug-code-gen >
                  pug-attrs > constantinople > babel-types > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   request-promise-native

  Path            request-promise-native > request-promise-core > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > async > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  High            Command Injection in lodash

  Package         lodash

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > gaze > globule > lodash

  More info       https://github.com/advisories/GHSA-35jh-r3h4-6jhm




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   bull

  Path            bull > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  http-proxy-middleware > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   email-templates

  Path            email-templates > preview-email > pug > pug-code-gen >
                  constantinople > babel-types > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   email-templates

  Path            email-templates > preview-email > pug > pug-code-gen >
                  pug-attrs > constantinople > babel-types > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   request-promise-native

  Path            request-promise-native > request-promise-core > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > async > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9




  Moderate        Regular Expression Denial of Service (ReDoS) in lodash

  Package         lodash

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > gaze > globule > lodash

  More info       https://github.com/advisories/GHSA-29mw-wpgm-hmr9



# Run  npm update browserify-sign --depth 5  to resolve 1 vulnerability

  High            browserify-sign upper bound check issue in `dsaVerify` leads
                  to a signature forgery attack

  Package         browserify-sign

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > node-libs-browser
                  > crypto-browserify > browserify-sign

  More info       https://github.com/advisories/GHSA-x9w5-v3q2-3rhw



# Run  npm update get-func-name --depth 2  to resolve 1 vulnerability

  High            Chaijs/get-func-name vulnerable to ReDoS

  Package         get-func-name

  Dependency of   chai [dev]

  Path            chai > get-func-name

  More info       https://github.com/advisories/GHSA-4q6p-r6v2-jvc5



# Run  npm update axios --depth 2  to resolve 1 vulnerability

  Moderate        Axios Cross-Site Request Forgery Vulnerability

  Package         axios

  Dependency of   facebook-nodejs-business-sdk

  Path            facebook-nodejs-business-sdk > axios

  More info       https://github.com/advisories/GHSA-wf5p-g6vw-rhxx



# Run  npm update eventsource --depth 4  to resolve 1 vulnerability

  Critical        Exposure of Sensitive Information in eventsource

  Package         eventsource

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server >
                  sockjs-client > eventsource

  More info       https://github.com/advisories/GHSA-6h5x-7c5m-7cr7



# Run  npm update glob-parent --depth 3  to resolve 1 vulnerability

  High            glob-parent vulnerable to Regular Expression Denial of
                  Service in enclosure regex

  Package         glob-parent

  Dependency of   mocha [dev]

  Path            mocha > chokidar > glob-parent

  More info       https://github.com/advisories/GHSA-ww39-953v-wcq6



# Run  npm update dns-packet --depth 5  to resolve 1 vulnerability

  High            Potential memory exposure in dns-packet

  Package         dns-packet

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > bonjour
                  > multicast-dns > dns-packet

  More info       https://github.com/advisories/GHSA-3wcq-x3mq-6r9p



# Run  npm update diff --depth 2  to resolve 1 vulnerability

  High            Regular Expression Denial of Service (ReDoS)

  Package         diff

  Dependency of   ts-node [dev]

  Path            ts-node > diff

  More info       https://github.com/advisories/GHSA-h6ch-v84p-w6p9



# Run  npm update jsprim --depth 8  to resolve 6 vulnerabilities

  Critical        json-schema is vulnerable to Prototype Pollution

  Package         json-schema

  Dependency of   request

  Path            request > http-signature > jsprim > json-schema

  More info       https://github.com/advisories/GHSA-896r-f27r-55mw




  Critical        json-schema is vulnerable to Prototype Pollution

  Package         json-schema

  Dependency of   fb

  Path            fb > request > http-signature > jsprim > json-schema

  More info       https://github.com/advisories/GHSA-896r-f27r-55mw




  Critical        json-schema is vulnerable to Prototype Pollution

  Package         json-schema

  Dependency of   cldr-data

  Path            cldr-data > cldr-data-downloader > request > http-signature
                  > jsprim > json-schema

  More info       https://github.com/advisories/GHSA-896r-f27r-55mw




  Critical        json-schema is vulnerable to Prototype Pollution

  Package         json-schema

  Dependency of   email-templates

  Path            email-templates > juice > web-resource-inliner > request >
                  http-signature > jsprim > json-schema

  More info       https://github.com/advisories/GHSA-896r-f27r-55mw




  Critical        json-schema is vulnerable to Prototype Pollution

  Package         json-schema

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp >
                  request > http-signature > jsprim > json-schema

  More info       https://github.com/advisories/GHSA-896r-f27r-55mw




  Critical        json-schema is vulnerable to Prototype Pollution

  Package         json-schema

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > request > http-signature >
                  jsprim > json-schema

  More info       https://github.com/advisories/GHSA-896r-f27r-55mw



# Run  npm update moment --depth 4  to resolve 10 vulnerabilities

  High            Moment.js vulnerable to Inefficient Regular Expression
                  Complexity

  Package         moment

  Dependency of   moment

  Path            moment

  More info       https://github.com/advisories/GHSA-wc69-rhjr-hc9g




  High            Moment.js vulnerable to Inefficient Regular Expression
                  Complexity

  Package         moment

  Dependency of   chart.js

  Path            chart.js > moment

  More info       https://github.com/advisories/GHSA-wc69-rhjr-hc9g




  High            Moment.js vulnerable to Inefficient Regular Expression
                  Complexity

  Package         moment

  Dependency of   cron

  Path            cron > moment-timezone > moment

  More info       https://github.com/advisories/GHSA-wc69-rhjr-hc9g




  High            Moment.js vulnerable to Inefficient Regular Expression
                  Complexity

  Package         moment

  Dependency of   bull

  Path            bull > cron-parser > moment-timezone > moment

  More info       https://github.com/advisories/GHSA-wc69-rhjr-hc9g




  High            Moment.js vulnerable to Inefficient Regular Expression
                  Complexity

  Package         moment

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > moment

  More info       https://github.com/advisories/GHSA-wc69-rhjr-hc9g




  High            Path Traversal: 'dir/../../filename' in moment.locale

  Package         moment

  Dependency of   moment

  Path            moment

  More info       https://github.com/advisories/GHSA-8hfj-j24r-96c4




  High            Path Traversal: 'dir/../../filename' in moment.locale

  Package         moment

  Dependency of   chart.js

  Path            chart.js > moment

  More info       https://github.com/advisories/GHSA-8hfj-j24r-96c4




  High            Path Traversal: 'dir/../../filename' in moment.locale

  Package         moment

  Dependency of   cron

  Path            cron > moment-timezone > moment

  More info       https://github.com/advisories/GHSA-8hfj-j24r-96c4




  High            Path Traversal: 'dir/../../filename' in moment.locale

  Package         moment

  Dependency of   bull

  Path            bull > cron-parser > moment-timezone > moment

  More info       https://github.com/advisories/GHSA-8hfj-j24r-96c4




  High            Path Traversal: 'dir/../../filename' in moment.locale

  Package         moment

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > moment

  More info       https://github.com/advisories/GHSA-8hfj-j24r-96c4



# Run  npm update node-fetch --depth 7  to resolve 7 vulnerabilities

  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g




  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   actions-on-google

  Path            actions-on-google > google-auth-library > gtoken > gaxios >
                  node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g




  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > google-auth-library >
                  gtoken > gaxios > node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g




  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > googleapis-common >
                  google-auth-library > gtoken > gaxios > node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g




  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   dialogflow

  Path            dialogflow > google-gax > node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g




  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gaxios >
                  node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g




  High            node-fetch forwards secure headers to untrusted sites

  Package         node-fetch

  Dependency of   dialogflow

  Path            dialogflow > google-gax > google-auth-library > gcp-metadata
                  > gaxios > node-fetch

  More info       https://github.com/advisories/GHSA-r683-j2x4-v87g



# Run  npm update y18n --depth 5  to resolve 3 vulnerabilities

  High            Prototype Pollution in y18n

  Package         y18n

  Dependency of   concurrently

  Path            concurrently > yargs > y18n

  More info       https://github.com/advisories/GHSA-c4w7-xm78-47vh




  High            Prototype Pollution in y18n

  Package         y18n

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > yargs >
                  y18n

  More info       https://github.com/advisories/GHSA-c4w7-xm78-47vh




  High            Prototype Pollution in y18n

  Package         y18n

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > cacache > y18n

  More info       https://github.com/advisories/GHSA-c4w7-xm78-47vh



# Run  npm update word-wrap --depth 7  to resolve 2 vulnerabilities

  Moderate        word-wrap vulnerable to Regular Expression Denial of Service

  Package         word-wrap

  Dependency of   blockly

  Path            blockly > jsdom > escodegen > optionator > word-wrap

  More info       https://github.com/advisories/GHSA-j8xg-fqg3-53r7




  Moderate        word-wrap vulnerable to Regular Expression Denial of Service

  Package         word-wrap

  Dependency of   @progress/kendo-angular-messages

  Path            @progress/kendo-angular-messages > xlf-translate > jsonpath
                  > static-eval > escodegen > optionator > word-wrap

  More info       https://github.com/advisories/GHSA-j8xg-fqg3-53r7



# Run  npm update underscore --depth 2  to resolve 2 vulnerabilities

  Critical        Arbitrary Code Execution in underscore

  Package         underscore

  Dependency of   underscore

  Path            underscore

  More info       https://github.com/advisories/GHSA-cf4h-3jhx-xvhq




  Critical        Arbitrary Code Execution in underscore

  Package         underscore

  Dependency of   country-data

  Path            country-data > underscore

  More info       https://github.com/advisories/GHSA-cf4h-3jhx-xvhq



# Run  npm update httpntlm --depth 2  to resolve 1 vulnerability

  Critical        Arbitrary Code Execution in underscore

  Package         underscore

  Dependency of   soap

  Path            soap > httpntlm > underscore

  More info       https://github.com/advisories/GHSA-cf4h-3jhx-xvhq



# Run  npm update tough-cookie --depth 3  to resolve 1 vulnerability

  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Dependency of   blockly

  Path            blockly > jsdom > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3



# Run  npm update protobufjs --depth 4  to resolve 2 vulnerabilities

  High            Prototype Pollution in protobufjs

  Package         protobufjs

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  protobufjs

  More info       https://github.com/advisories/GHSA-g954-5hwp-pp24




  Critical        protobufjs Prototype Pollution vulnerability

  Package         protobufjs

  Dependency of   firebase-admin

  Path            firebase-admin > @google-cloud/firestore > google-gax >
                  protobufjs

  More info       https://github.com/advisories/GHSA-h755-8qp9-cq85



# Run  npm update @babel/traverse --depth 6  to resolve 4 vulnerabilities

  Critical        Babel vulnerable to arbitrary code execution when compiling
                  specifically crafted malicious code

  Package         @babel/traverse

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > @babel/traverse

  More info       https://github.com/advisories/GHSA-67hx-6x53-jw92




  Critical        Babel vulnerable to arbitrary code execution when compiling
                  specifically crafted malicious code

  Package         @babel/traverse

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > @babel/helpers >
                  @babel/traverse

  More info       https://github.com/advisories/GHSA-67hx-6x53-jw92




  Critical        Babel vulnerable to arbitrary code execution when compiling
                  specifically crafted malicious code

  Package         @babel/traverse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > @babel/traverse

  More info       https://github.com/advisories/GHSA-67hx-6x53-jw92




  Critical        Babel vulnerable to arbitrary code execution when compiling
                  specifically crafted malicious code

  Package         @babel/traverse

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > @babel/helpers >
                  @babel/traverse

  More info       https://github.com/advisories/GHSA-67hx-6x53-jw92



# Run  npm update istanbul-lib-report --depth 2  to resolve 1 vulnerability

  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   karma-coverage-istanbul-reporter [dev]

  Path            karma-coverage-istanbul-reporter > istanbul-lib-report >
                  make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm update istanbul-reports --depth 2  to resolve 1 vulnerability

  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   karma-coverage-istanbul-reporter [dev]

  Path            karma-coverage-istanbul-reporter > istanbul-reports >
                  istanbul-lib-report > make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm update semver --depth 7  to resolve 21 vulnerabilities

  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   sharp

  Path            sharp > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > css-loader > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > postcss-loader > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > sass-loader > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > npm-package-arg > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > npm-package-arg > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > npm-package-arg
                  > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-pick-manifest > npm-package-arg > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > npm-pick-manifest > npm-install-checks >
                  semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-pick-manifest >
                  npm-install-checks > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/git > npm-pick-manifest >
                  npm-install-checks > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > @npmcli/git >
                  npm-pick-manifest > npm-install-checks > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/git > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/git > npm-pick-manifest >
                  semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/git > npm-pick-manifest >
                  npm-package-arg > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote > @npmcli/git >
                  npm-pick-manifest > npm-package-arg > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp >
                  semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > npm-pick-manifest > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > npm-pick-manifest > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw




  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-pick-manifest > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm update core-js-compat --depth 3  to resolve 1 vulnerability

  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > @babel/preset-env >
                  core-js-compat > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw



# Run  npm update minimatch --depth 10  to resolve 8 vulnerabilities

  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   @angular/localize

  Path            @angular/localize > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   protractor [dev]

  Path            protractor > jasmine > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > rimraf > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   protractor [dev]

  Path            protractor > webdriver-js-extender > selenium-webdriver >
                  rimraf > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > copy-webpack-plugin >
                  cacache > @npmcli/move-file > rimraf > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > cacache > move-concurrently > rimraf
                  > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > cacache > move-concurrently >
                  copy-concurrently > rimraf > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3




  High            minimatch ReDoS vulnerability

  Package         minimatch

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  npm-registry-fetch > make-fetch-happen > cacache >
                  @npmcli/move-file > rimraf > glob > minimatch

  More info       https://github.com/advisories/GHSA-f8q6-p94x-37v3



# Run  npm update qs --depth 4  to resolve 2 vulnerabilities

  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   actions-on-google

  Path            actions-on-google > googleapis > googleapis-common > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp




  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp



# Run  npm update express --depth 3  to resolve 2 vulnerabilities

  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > express
                  > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp




  High            qs vulnerable to Prototype Pollution

  Package         qs

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack-dev-server > express
                  > body-parser > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp



# Run  npm update minimist --depth 8  to resolve 26 vulnerabilities

  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > json5 > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @angular-devkit/build-optimizer > loader-utils > json5 >
                  minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > json5 > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   ts-node [dev]

  Path            ts-node > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > tar > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > cacache > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > cssnano >
                  cssnano-preset-default > postcss-svgo > svgo > mkdirp >
                  minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack >
                  terser-webpack-plugin > cacache > move-concurrently >
                  copy-concurrently > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   bcrypt

  Path            bcrypt > node-pre-gyp > rc > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   console-stamp

  Path            console-stamp > dateformat > meow > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   i18n

  Path            i18n > messageformat > make-plural > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   email-templates

  Path            email-templates > @ladjs/i18n > i18n > messageformat >
                  make-plural > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   email-templates

  Path            email-templates > html-to-text > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   email-templates

  Path            email-templates > preview-email > mailparser > html-to-text
                  > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   sharp

  Path            sharp > prebuild-install > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > babel-loader > loader-utils
                  > json5 > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > resolve-url-loader >
                  loader-utils > json5 > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > webpack > loader-utils >
                  json5 > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > worker-plugin > loader-utils
                  > json5 > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   @angular/compiler-cli [dev]

  Path            @angular/compiler-cli > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   csv-write-stream [dev]

  Path            csv-write-stream > ndjson > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   protractor [dev]

  Path            protractor > blocking-proxy > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   protractor [dev]

  Path            protractor > webdriver-manager > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   ts-node [dev]

  Path            ts-node > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h




  Critical        Prototype Pollution in minimist

  Package         minimist

  Dependency of   tslint [dev]

  Path            tslint > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h



# Run  npm update json5 --depth 5  to resolve 3 vulnerabilities

  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h




  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @angular-devkit/build-optimizer > loader-utils > json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h




  High            Prototype Pollution in JSON5 via Parse Method

  Package         json5

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @jsdevtools/coverage-istanbul-loader >
                  istanbul-lib-instrument > @babel/core > json5

  More info       https://github.com/advisories/GHSA-9c47-m6qq-7p4h



# Run  npm update log4js --depth 2  to resolve 1 vulnerability

  Moderate        Incorrect Default Permissions in log4js

  Package         log4js

  Dependency of   karma [dev]

  Path            karma > log4js

  More info       https://github.com/advisories/GHSA-82v2-mx6x-wq7q



# Run  npm update async --depth 4  to resolve 3 vulnerabilities

  High            Prototype Pollution in async

  Package         async

  Dependency of   async

  Path            async

  More info       https://github.com/advisories/GHSA-fwr7-v2mv-hh25




  High            Prototype Pollution in async

  Package         async

  Dependency of   email-templates

  Path            email-templates > juice > web-resource-inliner > async

  More info       https://github.com/advisories/GHSA-fwr7-v2mv-hh25




  High            Prototype Pollution in async

  Package         async

  Dependency of   grunt [dev]

  Path            grunt > grunt-legacy-util > async

  More info       https://github.com/advisories/GHSA-fwr7-v2mv-hh25



# Run  npm update crypto-js --depth 1  to resolve 1 vulnerability

  Critical        crypto-js PBKDF2 1,000 times weaker than specified in 1993
                  and 1.3M times weaker than current standard

  Package         crypto-js

  Dependency of   crypto-js

  Path            crypto-js

  More info       https://github.com/advisories/GHSA-xwcq-pm8m-c4vf




                                 Manual Review
             Some vulnerabilities require your attention to resolve

          Visit https://go.npm.me/audit-guide for additional guidance


  Low             Denial of Service in express-fileupload

  Package         express-fileupload

  Patched in      >=1.1.6-alpha.6

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-q3w9-g74q-vp5f


  Low             Denial of Service in express-fileupload

  Package         express-fileupload

  Patched in      >=1.1.6-alpha.6

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-q3w9-g74q-vp5f


  Low             Denial of Service in express-fileupload

  Package         express-fileupload

  Patched in      >=1.1.6-alpha.6

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-q3w9-g74q-vp5f


  Low             Denial of Service in express-fileupload

  Package         express-fileupload

  Patched in      >=1.1.6-alpha.6

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-q3w9-g74q-vp5f


  Low             Denial of Service in express-fileupload

  Package         express-fileupload

  Patched in      >=1.1.6-alpha.6

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-q3w9-g74q-vp5f


  Critical        Prototype Pollution in express-fileupload

  Package         express-fileupload

  Patched in      >=1.1.9

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-9wcg-jrwf-8gg7


  High            Express-FileUpload Arbitrary File Overwrite

  Package         express-fileupload

  Patched in      No patch available

  Dependency of   express-fileupload

  Path            express-fileupload

  More info       https://github.com/advisories/GHSA-w4m6-x6c2-j5c9


  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Patched in      >=6.12.3

  Dependency of   cldr-data

  Path            cldr-data > cldr-data-downloader > request > har-validator >
                  ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw


  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Patched in      >=6.12.3

  Dependency of   request

  Path            request > har-validator > ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw


  Moderate        Prototype Pollution in Ajv

  Package         ajv

  Patched in      >=6.12.3

  Dependency of   fb

  Path            fb > request > har-validator > ajv

  More info       https://github.com/advisories/GHSA-v88g-cgmw-v5xw


  High            Node-Redis potential exponential regex in monitor mode

  Package         redis

  Patched in      >=3.1.1

  Dependency of   rediscache

  Path            rediscache > redis

  More info       https://github.com/advisories/GHSA-35q2-47q7-3pc3


  Moderate        Misinterpretation of malicious XML input

  Package         xmldom

  Patched in      >=0.7.0

  Dependency of   @mapbox/togeojson

  Path            @mapbox/togeojson > xmldom

  More info       https://github.com/advisories/GHSA-5fg8-2547-mr8q


  Moderate        Misinterpretation of malicious XML input

  Package         xmldom

  Patched in      >=0.7.0

  Dependency of   xmldom

  Path            xmldom

  More info       https://github.com/advisories/GHSA-5fg8-2547-mr8q


  Moderate        Misinterpretation of malicious XML input

  Package         xmldom

  Patched in      >=0.7.0

  Dependency of   mybatisnodejs

  Path            mybatisnodejs > xmldom

  More info       https://github.com/advisories/GHSA-5fg8-2547-mr8q


  Moderate        Misinterpretation of malicious XML input

  Package         xmldom

  Patched in      >=0.7.0

  Dependency of   xml-crypto

  Path            xml-crypto > xmldom

  More info       https://github.com/advisories/GHSA-5fg8-2547-mr8q


  Moderate        Misinterpretation of malicious XML input

  Package         xmldom

  Patched in      >=0.7.0

  Dependency of   soap

  Path            soap > xml-crypto > xmldom

  More info       https://github.com/advisories/GHSA-5fg8-2547-mr8q


  Moderate        Misinterpretation of malicious XML input

  Package         xmldom

  Patched in      >=0.5.0

  Dependency of   @mapbox/togeojson

  Path            @mapbox/togeojson > xmldom

  More info       https://github.com/advisories/GHSA-h6q6-9hqw-rwfv


  Critical        xmldom allows multiple root nodes in a DOM

  Package         xmldom

  Patched in      No patch available

  Dependency of   @mapbox/togeojson

  Path            @mapbox/togeojson > xmldom

  More info       https://github.com/advisories/GHSA-crh6-fp67-6883


  Critical        xmldom allows multiple root nodes in a DOM

  Package         xmldom

  Patched in      No patch available

  Dependency of   xmldom

  Path            xmldom

  More info       https://github.com/advisories/GHSA-crh6-fp67-6883


  Critical        xmldom allows multiple root nodes in a DOM

  Package         xmldom

  Patched in      No patch available

  Dependency of   mybatisnodejs

  Path            mybatisnodejs > xmldom

  More info       https://github.com/advisories/GHSA-crh6-fp67-6883


  Critical        xmldom allows multiple root nodes in a DOM

  Package         xmldom

  Patched in      No patch available

  Dependency of   xml-crypto

  Path            xml-crypto > xmldom

  More info       https://github.com/advisories/GHSA-crh6-fp67-6883


  Critical        xmldom allows multiple root nodes in a DOM

  Package         xmldom

  Patched in      No patch available

  Dependency of   soap

  Path            soap > xml-crypto > xmldom

  More info       https://github.com/advisories/GHSA-crh6-fp67-6883


  Moderate        Axios vulnerable to Server-Side Request Forgery

  Package         axios

  Patched in      >=0.21.1

  Dependency of   tor-axios

  Path            tor-axios > axios

  More info       https://github.com/advisories/GHSA-4w2v-q235-vp99


  Moderate        Axios vulnerable to Server-Side Request Forgery

  Package         axios

  Patched in      >=0.21.1

  Dependency of   totalvoice-node

  Path            totalvoice-node > axios

  More info       https://github.com/advisories/GHSA-4w2v-q235-vp99


  Moderate        Axios Cross-Site Request Forgery Vulnerability

  Package         axios

  Patched in      >=1.6.0

  Dependency of   @types/axios [dev]

  Path            @types/axios > axios

  More info       https://github.com/advisories/GHSA-wf5p-g6vw-rhxx


  Moderate        Axios Cross-Site Request Forgery Vulnerability

  Package         axios

  Patched in      >=1.6.0

  Dependency of   tor-axios

  Path            tor-axios > axios

  More info       https://github.com/advisories/GHSA-wf5p-g6vw-rhxx


  Moderate        Axios Cross-Site Request Forgery Vulnerability

  Package         axios

  Patched in      >=1.6.0

  Dependency of   totalvoice-node

  Path            totalvoice-node > axios

  More info       https://github.com/advisories/GHSA-wf5p-g6vw-rhxx


  High            axios Inefficient Regular Expression Complexity
                  vulnerability

  Package         axios

  Patched in      >=0.21.2

  Dependency of   @types/axios [dev]

  Path            @types/axios > axios

  More info       https://github.com/advisories/GHSA-cph5-m8f7-6c5x


  High            axios Inefficient Regular Expression Complexity
                  vulnerability

  Package         axios

  Patched in      >=0.21.2

  Dependency of   tor-axios

  Path            tor-axios > axios

  More info       https://github.com/advisories/GHSA-cph5-m8f7-6c5x


  High            axios Inefficient Regular Expression Complexity
                  vulnerability

  Package         axios

  Patched in      >=0.21.2

  Dependency of   totalvoice-node

  Path            totalvoice-node > axios

  More info       https://github.com/advisories/GHSA-cph5-m8f7-6c5x


  Moderate        xml2js is vulnerable to prototype pollution

  Package         xml2js

  Patched in      >=0.5.0

  Dependency of   protractor [dev]

  Path            protractor > selenium-webdriver > xml2js

  More info       https://github.com/advisories/GHSA-776f-qx25-q3cc


  Moderate        xml2js is vulnerable to prototype pollution

  Package         xml2js

  Patched in      >=0.5.0

  Dependency of   protractor [dev]

  Path            protractor > webdriver-js-extender > selenium-webdriver >
                  xml2js

  More info       https://github.com/advisories/GHSA-776f-qx25-q3cc


  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in follow-redirects

  Package         follow-redirects

  Patched in      >=1.14.8

  Dependency of   tor-axios

  Path            tor-axios > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-pw2r-vq6v-hr8c


  Moderate        Exposure of Sensitive Information to an Unauthorized Actor
                  in follow-redirects

  Package         follow-redirects

  Patched in      >=1.14.8

  Dependency of   totalvoice-node

  Path            totalvoice-node > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-pw2r-vq6v-hr8c


  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Patched in      >=1.14.7

  Dependency of   tor-axios

  Path            tor-axios > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q


  High            Exposure of sensitive information in follow-redirects

  Package         follow-redirects

  Patched in      >=1.14.7

  Dependency of   totalvoice-node

  Path            totalvoice-node > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-74fj-2j2h-c42q


  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Patched in      >=1.15.4

  Dependency of   tor-axios

  Path            tor-axios > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc


  Moderate        Follow Redirects improperly handles URLs in the url.parse()
                  function

  Package         follow-redirects

  Patched in      >=1.15.4

  Dependency of   totalvoice-node

  Path            totalvoice-node > axios > follow-redirects

  More info       https://github.com/advisories/GHSA-jchw-25xp-jwwc


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   cldr-data

  Path            cldr-data > cldr-data-downloader > request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   request

  Path            request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   fb

  Path            fb > request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   mercadopago

  Path            mercadopago > request-etag > request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   email-templates

  Path            email-templates > juice > web-resource-inliner > request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > pacote > @npmcli/run-script > node-gyp >
                  request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > @schematics/update > pacote >
                  @npmcli/run-script > node-gyp > request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  Moderate        Server-Side Request Forgery in Request

  Package         request

  Patched in      No patch available

  Dependency of   @angular/cli [dev]

  Path            @angular/cli > universal-analytics > request

  More info       https://github.com/advisories/GHSA-p8p7-x288-28g6


  High            Crash in HeaderParser in dicer

  Package         dicer

  Patched in      No patch available

  Dependency of   express-fileupload

  Path            express-fileupload > busboy > dicer

  More info       https://github.com/advisories/GHSA-wm7h-9275-46v2


  High            Crash in HeaderParser in dicer

  Package         dicer

  Patched in      No patch available

  Dependency of   firebase-admin

  Path            firebase-admin > dicer

  More info       https://github.com/advisories/GHSA-wm7h-9275-46v2


  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Patched in      >=4.1.1

  Dependency of   qrcode

  Path            qrcode > yargs > string-width > strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw


  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Patched in      >=4.1.1

  Dependency of   qrcode

  Path            qrcode > yargs > cliui > string-width > strip-ansi >
                  ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw


  High            Inefficient Regular Expression Complexity in
                  chalk/ansi-regex

  Package         ansi-regex

  Patched in      >=4.1.1

  Dependency of   qrcode

  Path            qrcode > yargs > cliui > wrap-ansi > string-width >
                  strip-ansi > ansi-regex

  More info       https://github.com/advisories/GHSA-93q8-gq69-wqmw


  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Patched in      >=3.2.7

  Dependency of   protractor [dev]

  Path            protractor > browserstack > https-proxy-agent > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c


  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Patched in      >=3.2.7

  Dependency of   gm

  Path            gm > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c


  Moderate        Regular Expression Denial of Service in debug

  Package         debug

  Patched in      >=3.2.7

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > tiny-lr > debug

  More info       https://github.com/advisories/GHSA-gxpj-cx7g-858c


  Moderate        Cross site scripting in Angular

  Package         @angular/core

  Patched in      >=10.2.5

  Dependency of   codelyzer [dev]

  Path            codelyzer > @angular/core

  More info       https://github.com/advisories/GHSA-c75v-2vq8-878f


  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Patched in      >=4.1.3

  Dependency of   request-promise-native

  Path            request-promise-native > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3


  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Patched in      >=4.1.3

  Dependency of   cldr-data

  Path            cldr-data > cldr-data-downloader > request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3


  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Patched in      >=4.1.3

  Dependency of   request

  Path            request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3


  Moderate        tough-cookie Prototype Pollution vulnerability

  Package         tough-cookie

  Patched in      >=4.1.3

  Dependency of   fb

  Path            fb > request > tough-cookie

  More info       https://github.com/advisories/GHSA-72xf-g2v4-qvf3


  High            Inefficient Regular Expression Complexity in nth-check

  Package         nth-check

  Patched in      >=2.0.1

  Dependency of   cheerio

  Path            cheerio > css-select > nth-check

  More info       https://github.com/advisories/GHSA-rp65-9cf3-cjxr


  High            Inefficient Regular Expression Complexity in nth-check

  Package         nth-check

  Patched in      >=2.0.1

  Dependency of   @progress/kendo-angular-messages

  Path            @progress/kendo-angular-messages > cheerio > css-select >
                  nth-check

  More info       https://github.com/advisories/GHSA-rp65-9cf3-cjxr


  High            Inefficient Regular Expression Complexity in nth-check

  Package         nth-check

  Patched in      >=2.0.1

  Dependency of   email-templates

  Path            email-templates > juice > cheerio > css-select > nth-check

  More info       https://github.com/advisories/GHSA-rp65-9cf3-cjxr


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=5.7.2

  Dependency of   tslint [dev]

  Path            tslint > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=5.7.2

  Dependency of   @angular/localize

  Path            @angular/localize > @babel/core > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=5.7.2

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > @babel/core > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=5.7.2

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular >
                  @babel/plugin-transform-runtime > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=5.7.2

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > less > make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=5.7.2

  Dependency of   karma-coverage-istanbul-reporter [dev]

  Path            karma-coverage-istanbul-reporter > istanbul-lib-source-maps
                  > make-dir > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=6.3.1

  Dependency of   @angular-devkit/build-angular [dev]

  Path            @angular-devkit/build-angular > @babel/preset-env >
                  @babel/helper-compilation-targets > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  Moderate        semver vulnerable to Regular Expression Denial of Service

  Package         semver

  Patched in      >=6.3.1

  Dependency of   @angular/compiler-cli [dev]

  Path            @angular/compiler-cli > @babel/core > semver

  More info       https://github.com/advisories/GHSA-c2qf-rxjj-qqgw


  High            qs vulnerable to Prototype Pollution

  Package         qs

  Patched in      >=6.5.3

  Dependency of   request

  Path            request > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp


  High            qs vulnerable to Prototype Pollution

  Package         qs

  Patched in      >=6.5.3

  Dependency of   fb

  Path            fb > request > qs

  More info       https://github.com/advisories/GHSA-hrpp-h998-j3pp


  Moderate        Prototype Pollution in minimist

  Package         minimist

  Patched in      >=0.2.1

  Dependency of   cldr-data

  Path            cldr-data > cldr-data-downloader > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-vh95-rmgr-6w4m


  Critical        Prototype Pollution in minimist

  Package         minimist

  Patched in      >=0.2.4

  Dependency of   cldr-data

  Path            cldr-data > cldr-data-downloader > mkdirp > minimist

  More info       https://github.com/advisories/GHSA-xvch-5gv4-984h


  High            Prototype Pollution in lodash

  Package         lodash.set

  Patched in      No patch available

  Dependency of   express-jwt

  Path            express-jwt > lodash.set

  More info       https://github.com/advisories/GHSA-p6mc-m468-83gw


  High            Prototype Pollution in lodash

  Package         lodash.pick

  Patched in      No patch available

  Dependency of   @progress/kendo-angular-messages

  Path            @progress/kendo-angular-messages > cheerio > lodash.pick

  More info       https://github.com/advisories/GHSA-p6mc-m468-83gw


  High            Prototype Pollution in lodash

  Package         lodash.pick

  Patched in      No patch available

  Dependency of   email-templates

  Path            email-templates > juice > cheerio > lodash.pick

  More info       https://github.com/advisories/GHSA-p6mc-m468-83gw


  High            Prototype Pollution in async

  Package         async

  Patched in      >=2.6.4

  Dependency of   grunt-contrib-watch [dev]

  Path            grunt-contrib-watch > async

  More info       https://github.com/advisories/GHSA-fwr7-v2mv-hh25
