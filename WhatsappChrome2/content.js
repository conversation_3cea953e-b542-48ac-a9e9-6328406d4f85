// Content script para a extensão WhatsApp Suporte
// Este script é injetado na página do WhatsApp Web e interage com a interface
// Versão atualizada: 1.0.1 - Implementação de foco no iframe

// Configuração inicial
let config = {
  initialized: false,
  iframeInjected: false,
  currentChat: null
};

// Variável para manter o estado focado (simplificado)
let isIframeFocused = false;

// Função para elevar o iframe (aumentar z-index e adicionar classe de foco)
function elevarIframe(iframeContainer) {
  if (!iframeContainer) return;

  iframeContainer.style.zIndex = '3000';
  iframeContainer.classList.add('whatsapp-support-iframe-focused');
  // Não usamos mais data-lowered, agora verificamos diretamente se o menu está aberto
  //console.log('\n-------\nvai elevar o iframe\n');
  //console.log(iframeContainer);
  isIframeFocused = true;
}

// Função para abaixar o iframe (diminuir z-index e remover classe de foco)
function abaixarIframe(iframeContainer) {
  if (!iframeContainer) return;
  // Usamos z-index 99 em vez de 0 para manter o iframe acima do conteúdo normal
  // mas abaixo dos menus de contexto
  iframeContainer.style.zIndex = '99';
  iframeContainer.classList.remove('whatsapp-support-iframe-focused');
  // Não usamos mais data-lowered, agora verificamos diretamente se o menu está aberto
  isIframeFocused = false;
}

// Função para abaixar o iframe passivamente (sem marcar como intencionalmente abaixado)
function abaixarIframePassivo(iframeContainer) {
  if (!iframeContainer) return;
  // Usamos z-index 99 em vez de 0 para manter o iframe acima do conteúdo normal
  // mas abaixo dos menus de contexto
  iframeContainer.style.zIndex = '99';
  iframeContainer.classList.remove('whatsapp-support-iframe-focused');
  // Não definimos data-lowered aqui porque não é um abaixamento intencional
  isIframeFocused = false;
}

// Inicialização do script
async function initialize() {
  console.log('WhatsApp Suporte: Inicializando content script');

  // Verifica se já está inicializado
  if (config.initialized) return;

  // Verifica se estamos no WhatsApp
  const isWhatsApp = window.location.hostname.includes('whatsapp.com');

  if (!isWhatsApp) {
    console.log('WhatsApp Suporte: Script executado fora do WhatsApp Web. Ignorando inicialização.');
    return; // Sai se não estiver no WhatsApp
  }

  // Lógica específica para o WhatsApp
  try {
    // Aguarda o carregamento completo da página do WhatsApp
    await waitForWhatsAppLoad();
    console.log('WhatsApp carregado, configurando observadores');

    // Injeta o iframe APÓS o WhatsApp estar carregado
    injectIframe();

    // Carrega todos os scripts necessários usando o EnhancedScriptManager
    await window.scriptManager.loadAllScripts();

    setupEventListeners();
    config.initialized = true;

    // Configura o layout após o WhatsApp estar carregado
    const iframeContainer = document.getElementById('whatsapp-support-iframe-container');
    if (iframeContainer) {
      setupLayoutObserver(iframeContainer);
    }

    // Registra um callback para quando todos os scripts forem carregados
    window.scriptManager.onAllScriptsLoaded(() => {
      console.log('Todos os scripts foram carregados com sucesso!');
    });

    // Ouvir o evento personalizado do contexto da página
    window.addEventListener('wpp_initialized', function(event) {
      console.log('Evento wpp_initialized recebido:', event.detail);
    });

    // Notifica o background script que está pronto
    chrome.runtime.sendMessage({
      type: 'CONTENT_READY',
      data: { timestamp: new Date().toISOString() }
    });
  } catch (error) {
    console.error('Erro ao inicializar WhatsApp Suporte:', error);
    // Mesmo com erro, garante que o iframe esteja visível
    if (!config.iframeInjected) {
      injectIframe();
    }
  }
}

// Aguarda o carregamento completo do WhatsApp Web
function waitForWhatsAppLoad() {
  return new Promise((resolve, reject) => {
    // Verifica se o elemento principal do WhatsApp já existe
    const checkLoaded = () => {
      // Usa o seletor "chats-filled" conforme sugerido
      const appElement = document.querySelector('[data-testid="chats-filled"]') ||
                        document.querySelector('#pane-side') ||
                        document.querySelector('.two');

      if (appElement) {
        console.log('WhatsApp carregado: elemento encontrado');
        resolve();
      } else {
        console.log('Aguardando carregamento do WhatsApp...');
        // Verifica novamente após 1 segundo
        setTimeout(checkLoaded, 1000);
      }
    };

    // Inicia a verificação
    checkLoaded();

    // Timeout após 60 segundos
    setTimeout(() => {
      reject(new Error('Timeout ao aguardar carregamento do WhatsApp'));
    }, 60000);
  });
}

// Injeta o iframe com o aplicativo Angular
function injectIframe() {
  if (config.iframeInjected) return;

  // Cria o container do iframe
  const iframeContainer = document.createElement('div');
  iframeContainer.id = 'whatsapp-support-iframe-container';
  iframeContainer.className = 'whatsapp-support-iframe-container';

  // Cria o botão de configurações
  const settingsBtn = document.createElement('button');
  settingsBtn.className = 'whatsapp-support-iframe-settings-btn';
  settingsBtn.title = 'Configurar URL do iframe';
  settingsBtn.innerHTML = '<svg viewBox="0 0 24 24"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>';

  // Adiciona evento de clique ao botão
  settingsBtn.addEventListener('click', function() {
    const iframe = document.getElementById('whatsapp-support-iframe');
    if (iframe) {
      iframe.src = chrome.runtime.getURL('iframe-settings.html');
    }
  });

  // Adiciona o botão ao container
  iframeContainer.appendChild(settingsBtn);

  // Inicia com z-index mais baixo para não atrapalhar inicialmente
  iframeContainer.style.zIndex = '99';

  // Cria o iframe
  const iframe = document.createElement('iframe');
  iframe.id = 'whatsapp-support-iframe';

  // Verifica se existe uma URL salva no storage
  chrome.storage.local.get(['iframeCustomUrl'], function(result) {
    if (result.iframeCustomUrl) {
      // Se existir uma URL salva, usa ela
      iframe.src = result.iframeCustomUrl;
    } else {
      // Se não existir, usa a página de configurações
      iframe.src = chrome.runtime.getURL('iframe-settings.html');
    }
  });

  iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
  iframe.allowFullscreen = true;

  // Adiciona o iframe ao container
  iframeContainer.appendChild(iframe);

  // Adiciona o container ao DOM
  document.body.appendChild(iframeContainer);

  // Adiciona evento global para detectar cliques fora do iframe
  document.addEventListener('click', (event) => {
    const iframeContainer = document.getElementById('whatsapp-support-iframe-container');
    if (!iframeContainer) return; // Se não há container, não há nada a fazer

    // Verifica se há algum menu do WhatsApp aberto
    if (temMenuWhatsappAberto()) {
      // Se há menu aberto, não interferimos com o clique
      return;
    }

    // Verifica se o clique está dentro ou fora do iframe usando coordenadas
    const rect = iframeContainer.getBoundingClientRect();
    const isClickInsideIframe = isMouseOverIframe(event.clientX, event.clientY, rect);

    // Se o clique foi fora do iframe, abaixa o iframe passivamente
    if (!isClickInsideIframe) {
      abaixarIframePassivo(iframeContainer);
    } else {
      elevarIframe(iframeContainer);
    }
  });

  // Adiciona evento de mousemove para detectar quando o mouse passa sobre o iframe
  document.addEventListener('mousemove', (event) => {
    const iframeContainer = document.getElementById('whatsapp-support-iframe-container');
    if (!iframeContainer) return;

    // Verifica se há algum menu do WhatsApp aberto
    if (temMenuWhatsappAberto()) {
      // Se há menu aberto, não interferimos com o mousemove
      return;
    }

    // Verifica se o mouse está sobre um diálogo do WhatsApp
    const isOverDialog = event.target.closest('[role="dialog"], [data-animate-modal-popup="true"], .overlay, ._3J6wB');
    if (isOverDialog) {
      return; // Não altera o z-index se estiver sobre um diálogo
    }

    const rect = iframeContainer.getBoundingClientRect();
    const isHoveringIframe = isMouseOverIframe(event.clientX, event.clientY, rect);

    // Se o mouse está sobre o iframe e o iframe não está focado
    if (isHoveringIframe && !iframeContainer.classList.contains('whatsapp-support-iframe-focused')) {
      elevarIframe(iframeContainer);
    }
  }, false);

  // Adiciona um listener no elemento #app (ou um wrapper adequado)
  // para detectar cliques que deveriam ir para o iframe quando ele está com z-index baixo
  let appAttachAttempts = 0;
  const maxAttachAttempts = 5;

  function attachAppClickHandler() {
    const appElement = document.querySelector('#app');
    if (appElement) {
      // Use capturing phase to intercept clicks early
      appElement.addEventListener('click', (event) => {
        const iframeContainer = document.getElementById('whatsapp-support-iframe-container');
        if (!iframeContainer) return; // Safety check

        // Check if the click target is part of a known overlay
        const clickedElement = event.target;
        // Heuristic: Check for common overlay class names or attributes.
        // This might need refinement based on WhatsApp Web's actual structure.
        const isOverlay = clickedElement.closest('[class*="overlay"], [class*="modal"], [role="dialog"], [data-animate-modal-popup="true"]');

        if (!isOverlay) {
          const rect = iframeContainer.getBoundingClientRect();
          const isClickOnIframeArea = isMouseOverIframe(event.clientX, event.clientY, rect); // Keep using isMouseOverIframe here

          // Se o clique foi na área do iframe (mesmo que o z-index esteja baixo) E NÃO em um overlay
          if (isClickOnIframeArea) {
            elevarIframe(iframeContainer);
          }
        }
      }, true); // Use capture phase
      console.log('Successfully attached iframe click handler to #app element');
    } else {
      appAttachAttempts++;
      if (appAttachAttempts < maxAttachAttempts) {
        console.log(`[DEBUG] Attempt ${appAttachAttempts}/${maxAttachAttempts}: Waiting for #app element to attach iframe click handler`);
        // Try again after a short delay
        setTimeout(attachAppClickHandler, 500);
      } else {
        console.warn('[DEBUG] Could not find #app element to attach iframe click handler after multiple attempts');
      }
    }
  }

  // Start the attachment process
  attachAppClickHandler();

  // Adiciona handle de redimensionamento
  const resizer = document.createElement('div');
  resizer.id = 'whatsapp-support-iframe-resizer';
  resizer.className = 'whatsapp-support-iframe-resizer';
  resizer.setAttribute('title', 'Arraste para ajustar a largura do painel');
  iframeContainer.appendChild(resizer);

  // Adiciona lógica de redimensionamento
  addResizeLogic(iframeContainer, resizer);

  // Adiciona estilos CSS para o iframe e o resizer
  const styleElement = document.createElement('style');
  styleElement.textContent = `
    .whatsapp-support-iframe-container {
      position: fixed;
      top: 0;
      right: 0;
      width: 400px; /* Largura inicial */
      height: 100%;
      /* z-index é gerenciado dinamicamente */
      box-shadow: 0px 0 2px rgba(0, 0, 0, 0.3);
      background: white;
      border: none;
      border-left: 1px solid #ccc;
      transition: z-index 0.1s ease, box-shadow 0.3s ease, border-left-color 0.3s ease;
    }

    /* Estilo aplicado quando o iframe está focado ou com mouse sobre */
    .whatsapp-support-iframe-focused {
      box-shadow: 0px 0 12px rgba(0, 0, 0, 0.6);
      border-left: 1px solid #0084ff; /* Cor de destaque */
    }

    /* Garante que os menus de contexto do WhatsApp fiquem visíveis sobre o iframe */
    .app-wrapper-web {
      overflow: visible !important;
    }

    /* Ajusta posição do ::after para não cobrir o iframe */
    .app-wrapper-web::after {
      position: absolute !important;
    }

    .whatsapp-support-iframe-resizer {
      position: absolute;
      left: -10px; /* Posição do handle */
      top: 0;
      width: 18px; /* Largura clicável do handle */
      height: 100%;
      cursor: col-resize;
      z-index: 1001; /* Garante que o resizer fique sobre o iframe */
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.25s ease; /* Transição suave */
    }

    /* Linha visual do resizer */
    .whatsapp-support-iframe-resizer::before {
      content: "";
      position: absolute;
      left: 9px;
      display: block;
      width: 1px;
      height: 100%;
      background-color: #f0f0f5;
      box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
    }

    /* Feedback visual ao passar o mouse ou redimensionar */
    .whatsapp-support-iframe-resizer:hover,
    .whatsapp-support-iframe-resizer.resizing {
      background-color: rgba(0, 0, 0, 0.07);
    }

    /* Barra visual interna do resizer */
    .whatsapp-support-iframe-resizer::after {
      content: "";
      position: absolute;
      left: 6px;
      width: 5px;
      height: 50px;
      background: #B8B8B8; /* Cor padrão */
      border-radius: 2px;
      pointer-events: none; /* Não interfere no clique */
      opacity: 0.7;
      transition: all 0.3s ease;
    }

    .whatsapp-support-iframe-resizer:hover::after,
    .whatsapp-support-iframe-resizer.resizing::after {
      background: #555; /* Cor ao interagir */
      opacity: 1;
    }

    #whatsapp-support-iframe {
      width: 100%;
      height: 100%;
      border: none;
      background: white;
    }

  `;
  // Não adiciona mais regras @media aqui, o ajuste é feito dinamicamente

  document.head.appendChild(styleElement);

  // Adiciona classe ao body para indicar que o iframe está presente
  document.body.classList.add('has-support-iframe');

  config.iframeInjected = true;
  console.log('Iframe injetado e configurado com sucesso');
}

// Função para adicionar a lógica de redimensionamento
function addResizeLogic(container, resizer) {
  let isResizing = false;
  let startX, startWidth;
  const minWidth = 150; // Largura mínima
  const maxWidth = 800; // Largura máxima

  resizer.addEventListener('mousedown', (e) => {
    // Verifica se é o botão esquerdo do mouse (button === 0)
    if (e.button !== 0) return;

    isResizing = true;
    resizer.classList.add('resizing'); // Adiciona classe durante o resize
    startX = e.clientX;
    startWidth = parseInt(document.defaultView.getComputedStyle(container).width, 10);
    document.body.style.userSelect = 'none'; // Evita seleção de texto durante o resize
    document.body.style.pointerEvents = 'none'; // Melhora performance durante resize

    // Adiciona um estilo global para forçar o cursor col-resize durante o arrasto
    const style = document.createElement('style');
    style.id = 'resize-cursor-style';
    style.innerHTML = '* { cursor: col-resize !important; }';
    document.head.appendChild(style);

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // Previne o comportamento padrão do navegador para evitar menu de contexto
    e.preventDefault();
  });

  function handleMouseMove(e) {
    if (!isResizing) return;

    const currentX = e.clientX;
    const diffX = startX - currentX;
    let newWidth = startWidth + diffX;

    // Aplica limites mínimo e máximo
    newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));

    container.style.width = `${newWidth}px`;

    // Ajusta diretamente a última div do app-wrapper-web
    ajustarUltimaDiv(`${newWidth}px`);
  }

  function handleMouseUp() {
    if (!isResizing) return;
    isResizing = false;
    resizer.classList.remove('resizing'); // Remove a classe após o resize
    document.body.style.userSelect = '';
    document.body.style.pointerEvents = '';

    // Remove o estilo global de cursor
    const cursorStyle = document.getElementById('resize-cursor-style');
    if (cursorStyle) {
      cursorStyle.parentNode.removeChild(cursorStyle);
    }

    //document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);

    // Salvar a nova largura
    const finalWidth = parseInt(container.style.width, 10);
    localStorage.setItem('whatsappSupportIframeWidth', finalWidth.toString());
  }

  // Carregar largura salva ao inicializar
  const savedWidthString = localStorage.getItem('whatsappSupportIframeWidth');
  if (savedWidthString) {
    const savedWidth = Math.max(minWidth, Math.min(parseInt(savedWidthString, 10), maxWidth));
    if (!isNaN(savedWidth)) {
      container.style.width = `${savedWidth}px`;
      ajustarUltimaDiv(`${savedWidth}px`);
    }
  }
}

// Função para garantir que o layout do WhatsApp seja ajustado apenas quando necessário
function setupLayoutObserver(container) {
  // Ajusta o layout inicial após o carregamento do WhatsApp
  updateWhatsAppLayout(container);

  // Adiciona listener para redimensionamento da janela
  window.addEventListener('resize', () => {
    updateWhatsAppLayout(container);
  });
}

// Função para atualizar o layout do WhatsApp com base na largura do iframe
function updateWhatsAppLayout(container) {
  console.log('Atualizando layout do WhatsApp Web');

  // Obtém a largura atual do container
  const width = parseInt(container.style.width || localStorage.getItem('whatsappSupportIframeWidth') || '400', 10);

  if (isNaN(width) || width <= 0) {
    console.warn('Largura inválida para o container:', width);
    return;
  }

  // Garante que o container tenha a largura correta
  container.style.setProperty('width', `${width}px`, 'important');

  // Aplica overflow: visible diretamente no elemento app-wrapper-web
  const appWrapper = document.querySelector('.app-wrapper-web');
  if (!appWrapper) {
    console.warn('Elemento app-wrapper-web não encontrado');
    return;
  }

  appWrapper.style.setProperty('overflow', 'visible', 'important');

  // Ajusta a última div do app-wrapper-web
  const divs = Array.from(appWrapper.children).filter(el => el.tagName === 'DIV');
  if (divs.length === 0) {
    console.warn('Nenhuma div filha encontrada em app-wrapper-web');
    return;
  }

  const ultimaDiv = divs[divs.length - 1];
  ultimaDiv.style.setProperty('width', `calc(100% - ${width}px)`, 'important');

  // Ajusta também o pseudo-elemento ::after
  ajustarPseudoElemento(appWrapper, `${width}px`);

  console.log('Layout do WhatsApp Web atualizado com sucesso');
}

// --- Ajuste do width da última div filha de .app-wrapper-web ---
function ajustarUltimaDiv(largura) {
  const appWrapper = document.querySelector('.app-wrapper-web');
  if (!appWrapper) return;
  // Seleciona apenas as divs filhas diretas (ignora sub-filhas)
  const divs = Array.from(appWrapper.children).filter(el => el.tagName === 'DIV');
  if (divs.length === 0) return;
  const ultimaDiv = divs[divs.length - 1];
  ultimaDiv.style.width = `calc(100% - ${largura})`;

  // Ajusta também o pseudo-elemento ::after
  ajustarPseudoElemento(appWrapper, largura);
}

// Função para ajustar o pseudo-elemento ::after
function ajustarPseudoElemento(appWrapper, largura) {
  // Cria ou atualiza o estilo para o pseudo-elemento
  let styleElement = document.getElementById('whatsapp-support-pseudo-style');

  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'whatsapp-support-pseudo-style';
    document.head.appendChild(styleElement);
  }

  // Define o estilo para o pseudo-elemento ::after
  styleElement.textContent = `
    .app-wrapper-web::after {
      width: calc(100% - ${largura}) !important;
    }
  `;
}

// Configura listeners de eventos
function setupEventListeners() {
  // Listener para mensagens do background script
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Mensagem recebida no content.js:', message);

    if (message.action === 'SYNC_REQUEST') {
      // Realiza sincronização quando solicitado
      processMessageQueue();
      sendResponse({ status: 'sync_started' });
    } else if (message.type === 'IFRAME_URL_UPDATED') {
      // Atualiza a URL do iframe quando ela for alterada nas configurações
      console.log('Recebida mensagem para atualizar URL do iframe:', message.url);

      const iframe = document.getElementById('whatsapp-support-iframe');
      if (iframe && message.url) {
        // Remove o iframe existente
        const container = document.getElementById('whatsapp-support-iframe-container');
        if (container) {
          container.removeChild(iframe);
        }

        // Cria um novo iframe com a nova URL
        const newIframe = document.createElement('iframe');
        newIframe.id = 'whatsapp-support-iframe';
        newIframe.src = message.url;
        newIframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
        newIframe.allowFullscreen = true;

        // Adiciona o novo iframe ao container
        container.appendChild(newIframe);

        // Verifica a integridade dos scripts e recarrega apenas os que tiverem problemas
        window.scriptManager.verifyAllScriptsIntegrity()
          .then(integrityStatus => {
            console.log('Status de integridade dos scripts:', integrityStatus);
            return window.scriptManager.reloadScriptsWithIntegrityIssues();
          })
          .then(() => {
            console.log('Scripts verificados e recarregados conforme necessário');
          })
          .catch(error => {
            console.error('Erro ao verificar/recarregar scripts:', error);
          });

        console.log('URL do iframe atualizada com sucesso');
      } else {
        console.warn('Iframe não encontrado ou URL não fornecida', iframe, message.url);
        alert('Erro: Iframe não encontrado ou URL não fornecida');
      }

      sendResponse({ status: 'URL atualizada com sucesso' });
    }
    return true; // Importante para manter o canal de comunicação aberto para resposta assíncrona
  });
}

// Inicia o script
initialize();

// Não é necessário adicionar outro listener 'load', initialize() já é chamado no fluxo correto.
// window.addEventListener('load', initialize);

// Exporta funções para testes (se necessário)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    initialize
  };
}

// Função simplificada para verificar se o mouse está sobre o iframe
function isMouseOverIframe(mouseX, mouseY, rect) {
  return (
    mouseX >= rect.left &&
    mouseX <= rect.right &&
    mouseY >= rect.top &&
    mouseY <= rect.bottom
  );
}

// Função para verificar se algum menu do WhatsApp está aberto
function temMenuWhatsappAberto() {
  const apps = document.querySelectorAll('div[role="application"]');
  const aberto = Array.from(apps).some(app => app.querySelector(':scope > ul') !== null);

  //console.log('Menu do WhatsApp aberto?', aberto);

  return aberto;
}
